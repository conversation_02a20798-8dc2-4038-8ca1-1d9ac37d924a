package com.anytech.anytxn.authorization.service.channel.dci;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.constants.Constants8583Field;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.enums.DciMTIEnum;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeSelfMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DciAuthTransPreprocessServiceImpl单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DCI授权交易预处理服务测试")
class DciAuthTransPreprocessServiceImplTest {

    @Mock
    private RuleTransferImpl ruleTransferService;

    @Mock
    private ParmCurrencyCodeSelfMapper parmCurrencyCodeSelfMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @InjectMocks
    private DciAuthTransPreprocessServiceImpl dciAuthTransPreprocessService;

    private ISO8583DTO iso8583DTO;
    private Map<Integer, String> fieldMap;
    private Map<String, String> transIdentifyMap;
    private ParmCurrencyCode currencyCode;

    @BeforeEach
    void setUp() {
        // 清理ThreadLocal
        AuthThreadLocalManager.remove();
        
        // 初始化测试数据
        iso8583DTO = new ISO8583DTO();
        fieldMap = new HashMap<>();
        transIdentifyMap = new HashMap<>();
        currencyCode = new ParmCurrencyCode();
        
        // 设置基础字段
        fieldMap.put(Constants8583Field.FIELD2, "1234567890123456");
        fieldMap.put(Constants8583Field.FIELD3, "000000");
        fieldMap.put(Constants8583Field.FIELD4, "100000");
        fieldMap.put(Constants8583Field.FIELD11, "123456");
        fieldMap.put(Constants8583Field.FIELD12, "123456");
        fieldMap.put(Constants8583Field.FIELD49, "702");
        
        iso8583DTO.setFieldMap(fieldMap);
        iso8583DTO.setMTI("1100");
        iso8583DTO.setSourceCode("DCI");
        
        // 设置交易识别结果
        transIdentifyMap.put(AuthConstans.AUTH_TRANS_TYPE_TOP_CODE, "01");
        transIdentifyMap.put(AuthConstans.AUTH_TRANS_TYPE_DETAIL_CODE, "001");
        transIdentifyMap.put(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE, "100");
        transIdentifyMap.put(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE_DEV, "200");
        
        // 设置货币信息
        currencyCode.setDecimalPlace(2);
    }

    @Test
    @DisplayName("正常授权请求预处理 - 成功场景")
    void testPreProcessAuthTrans_Success() throws IOException {
        // Given
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);
            
            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);
            
            // Then
            assertNotNull(result);
            assertEquals("01", result.getAuthTransactionTypeTopCode());
            assertEquals("001", result.getAuthTransactionTypeDetailCode());
            assertEquals("100", result.getPostingTransactionCode());
            assertEquals("200", result.getPostingTransactionCodeRev());
            assertEquals("123456789", result.getAuthGlobalFlowNumber());
            assertEquals("123456789", result.getCurrentAuthLogId());
            assertEquals("1100", result.getAuthMessageTypeId());
            assertEquals("1234567890123456", result.getAuthCardNumber());
            assertEquals("000000", result.getAuthProcessingCode());
            assertEquals(0, new BigDecimal("1000.00").compareTo(result.getAuthTransactionAmount()));
            assertEquals("DCI", result.getAuthTransactionSourceCode());
            
            verify(ruleTransferService).getTransIdentifyCheckRule("001", iso8583DTO);
            verify(sequenceIdGen, times(2)).generateId("tenant001");
            verify(parmCurrencyCodeSelfMapper).selectByCurrencyCode("702");
        }
    }

    @Test
    @DisplayName("DCI冲正通知预处理 - 跳过交易识别")
    void testPreProcessAuthTrans_ReversalAdvice() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_REVERSAL_ADVICE.getCode());

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertNull(result.getAuthTransactionTypeTopCode());
            assertNull(result.getAuthTransactionTypeDetailCode());
            assertEquals(DciMTIEnum.DCI_REVERSAL_ADVICE.getCode(), result.getAuthMessageTypeId());

            verify(ruleTransferService, never()).getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class));
        }
    }

    @Test
    @DisplayName("DCI文件操作预处理 - 跳过交易识别")
    void testPreProcessAuthTrans_FileAction() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_FILE_ACTION.getCode());

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertNull(result.getAuthTransactionTypeTopCode());
            assertEquals(DciMTIEnum.DCI_FILE_ACTION.getCode(), result.getAuthMessageTypeId());

            verify(ruleTransferService, never()).getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class));
        }
    }

    @Test
    @DisplayName("DCI管理通知预处理 - 跳过交易识别")
    void testPreProcessAuthTrans_AdministrativeAdvice() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_ADMINISTRATIVE_ADVICE.getCode());

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(DciMTIEnum.DCI_ADMINISTRATIVE_ADVICE.getCode(), result.getAuthMessageTypeId());

            verify(ruleTransferService, never()).getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class));
        }
    }

    @Test
    @DisplayName("08开头MTI预处理 - 跳过交易识别")
    void testPreProcessAuthTrans_MTIStartsWith08() throws IOException {
        // Given
        iso8583DTO.setMTI("0800");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("0800", result.getAuthMessageTypeId());

            verify(ruleTransferService, never()).getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class));
        }
    }

    @Test
    @DisplayName("18开头MTI预处理 - 跳过交易识别")
    void testPreProcessAuthTrans_MTIStartsWith18() throws IOException {
        // Given
        iso8583DTO.setMTI("1800");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("1800", result.getAuthMessageTypeId());

            verify(ruleTransferService, never()).getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class));
        }
    }

    @Test
    @DisplayName("空ISO8583DTO预处理 - 抛出NullPointerException")
    void testPreProcessAuthTrans_NullISO8583DTO() {
        // Given & When & Then
        assertThrows(NullPointerException.class, () -> {
            dciAuthTransPreprocessService.preProcessAuthTrans(null);
        });
    }

    @Test
    @DisplayName("交易金额为空或非数字 - 设置为零")
    void testPreProcessAuthTrans_EmptyOrNonNumericAmount() throws IOException {
        // Given
        fieldMap.put(Constants8583Field.FIELD4, "");
        iso8583DTO.setFieldMap(fieldMap);
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            
            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);
            
            // Then
            assertNotNull(result);
            assertEquals(BigDecimal.ZERO, result.getAuthTransactionAmount());
            
            verify(parmCurrencyCodeSelfMapper, never()).selectByCurrencyCode(anyString());
        }
    }

    @Test
    @DisplayName("货币代码为空 - 使用默认货币702")
    void testPreProcessAuthTrans_EmptyCurrencyCode() throws IOException {
        // Given
        fieldMap.remove(Constants8583Field.FIELD49);
        iso8583DTO.setFieldMap(fieldMap);

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(0, new BigDecimal("1000.00").compareTo(result.getAuthTransactionAmount()));

            verify(parmCurrencyCodeSelfMapper).selectByCurrencyCode("702");
        }
    }

    @Test
    @DisplayName("货币信息为空 - 使用默认小数位2")
    void testPreProcessAuthTrans_NullCurrencyCode() throws IOException {
        // Given
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(null);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(0, new BigDecimal("1000.00").compareTo(result.getAuthTransactionAmount()));

            verify(parmCurrencyCodeSelfMapper).selectByCurrencyCode("702");
        }
    }

    @Test
    @DisplayName("DCI授权通知交易类型构建 - 预授权完成")
    void testBuildTransType_DciAuthAdvice() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_AUTH_ADVICE.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "000000");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.AUNTH_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.NORMAL_TRANS.getCode(), result.getAuthTransactionTypeCode());
            assertEquals(com.anytech.anytxn.authorization.base.enums.PreAuthTransTypeEnum.PRE_COMPLETED.getCode(),
                        result.getPreauthTxnType());
            assertTrue(result.getPreAuthComplete());
        }
    }

    @Test
    @DisplayName("DCI授权请求交易类型构建 - 20开头退货交易")
    void testBuildTransType_DciAuthRequest_RefundsTrans() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_AUTH_REQUEST.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "200000");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.REFUNDS_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.REFUNDS_TRANS.getCode(), result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("DCI授权请求交易类型构建 - 02开头撤销交易")
    void testBuildTransType_DciAuthRequest_RevocationTrans() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_AUTH_REQUEST.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "020000");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.REVOCATION_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.REVOCATION_TRANS.getCode(), result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("DCI授权请求交易类型构建 - 普通交易")
    void testBuildTransType_DciAuthRequest_NormalTrans() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_AUTH_REQUEST.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "000000");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.AUNTH_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.NORMAL_TRANS.getCode(), result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("DCI冲正通知交易类型构建 - 20开头撤销冲正")
    void testBuildTransType_DciReversalAdvice_RevocationReversal() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_REVERSAL_ADVICE.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "200000");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.REVOCATION_REVERSAL_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode(), result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("DCI冲正通知交易类型构建 - 17开头撤销冲正")
    void testBuildTransType_DciReversalAdvice_RevocationReversal17() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_REVERSAL_ADVICE.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "170000");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.REVOCATION_REVERSAL_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.REVOCATION_REVERSAL_TRANS.getCode(), result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("DCI冲正通知交易类型构建 - 00开头普通冲正")
    void testBuildTransType_DciReversalAdvice_NormalReversal() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_REVERSAL_ADVICE.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "000000");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.REVERSAL_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.REVERSAL_TRANS.getCode(), result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("DCI冲正通知交易类型构建 - 01开头普通冲正")
    void testBuildTransType_DciReversalAdvice_NormalReversal01() throws IOException {
        // Given
        iso8583DTO.setMTI(DciMTIEnum.DCI_REVERSAL_ADVICE.getCode());
        fieldMap.put(Constants8583Field.FIELD3, "010000");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(ReversalTypeEnum.REVERSAL_TRANS.getCode(), result.getAuthReversalType());
            assertEquals(AuthTransTypeEnum.REVERSAL_TRANS.getCode(), result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("1开头MTI字段处理 - 设置额外字段")
    void testBuildAuthRecorded_MTIStartsWith1() throws IOException {
        // Given
        iso8583DTO.setMTI("1100");
        fieldMap.put(Constants8583Field.FIELD6, "50000");
        fieldMap.put(Constants8583Field.FIELD7, "0801123456");
        fieldMap.put(Constants8583Field.FIELD15, "0801");
        fieldMap.put(Constants8583Field.FIELD22, "123456789012");
        fieldMap.put(Constants8583Field.FIELD26, "5411");
        fieldMap.put(Constants8583Field.FIELD30, "702000000100000");
        fieldMap.put(Constants8583Field.FIELD32, "123456");
        fieldMap.put(Constants8583Field.FIELD33, "654321");
        fieldMap.put(Constants8583Field.FIELD40, "000");
        fieldMap.put(Constants8583Field.FIELD43, "TEST MERCHANT");
        fieldMap.put(Constants8583Field.FIELD44, "ADDITIONAL DATA");
        fieldMap.put(Constants8583Field.FIELD46, "C0000100000");
        fieldMap.put(Constants8583Field.FIELD48, "ADDITIONAL DATA");
        fieldMap.put(Constants8583Field.FIELD51, "702");
        fieldMap.put(Constants8583Field.FIELD56, "110012345608011234561234567890123");
        fieldMap.put(Constants8583Field.FIELD58, "AGENT001");
        fieldMap.put(Constants8583Field.FIELD59, "TRANSPORT DATA");
        fieldMap.put(Constants8583Field.FIELD62, "AUTH DATA");
        fieldMap.put(Constants8583Field.FIELD64, "MAC12345");
        fieldMap.put(Constants8583Field.FIELD72, "DATA RECORD");
        fieldMap.put(Constants8583Field.FIELD92, "702");
        fieldMap.put(Constants8583Field.FIELD93, "DEST001");
        fieldMap.put(Constants8583Field.FIELD94, "ORIG001");
        fieldMap.put(Constants8583Field.FIELD96, "KEY DATA");
        fieldMap.put(Constants8583Field.FIELD100, "RECV001");
        fieldMap.put(Constants8583Field.FIELD101, "FILENAME");
        fieldMap.put(Constants8583Field.FIELD104, "TRANS SPECIFIC");
        fieldMap.put(Constants8583Field.FIELD106, "TRANSACTIONAL");
        fieldMap.put(Constants8583Field.FIELD122, "AUTH AUTH DATA");
        fieldMap.put(Constants8583Field.FIELD123, "NETWORK REF");
        fieldMap.put(Constants8583Field.FIELD124, "TRANS QUALIFIER");
        fieldMap.put(Constants8583Field.FIELD125, "REPLACEMENT PIN");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals(0, new BigDecimal("500.00").compareTo(result.getAuthCardholderBillingAmount()));
            assertEquals("0801123456", result.getAuthTransmissionTime());
            assertEquals("0801", result.getAuthLocalTransactionDate());
            assertEquals("0801", result.getAuthSettlementDate());
            assertEquals("123456789012", result.getAuthServicePointCardCode());
            assertEquals("123456789012", result.getAuthServicePointEntryModeCode());
            assertEquals("2", result.getEncryptionMethodUsed());
            assertEquals("5411", result.getAuthCardAcceptorBusinessCode());
            assertEquals("5411", result.getAuthMerchantType());
            assertEquals("702", result.getAuthMerchantCountryCode());
            assertEquals("702000000100000", result.getAuthAmountsOfOrigina());
            assertEquals("123456", result.getAuthAcquiringIdentificationCode());
            assertEquals("654321", result.getAuthForwardingIdentificationCode());
            assertEquals("000", result.getAuthServiceCode());
            assertEquals("TEST MERCHANT", result.getAuthCardAcceptorNameLocation());
            assertEquals("ADDITIONAL DATA", result.getAuthAdditionalResponseData());
            assertEquals("C", result.getAuthTransactionFeeIndicator());
            assertEquals("0000100000", result.getAuthTransactionFee());
            assertEquals("ADDITIONAL DATA", result.getAdditionalData());
            assertEquals("702", result.getAuthBillingCurrencyCode());
            assertEquals("1100", result.getAuthOriginalMessageTypeId());
            assertEquals("123456", result.getAuthOriginalSystemTraceAuditNumber());
            assertEquals("080112345612", result.getAuthOriginalTransmissionTime());
            assertEquals("34567890123", result.getAuthOriginalAcquiringIdentificationCode());
        }
    }

    @Test
    @DisplayName("0开头MTI字段处理 - 设置POS相关字段")
    void testBuildAuthRecorded_MTIStartsWith0() throws IOException {
        // Given
        iso8583DTO.setMTI("0100");
        fieldMap.put(Constants8583Field.FIELD22, "12345");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("12", result.getAuthServicePointCardCode());
            assertEquals("3", result.getAuthServicePointPinCode());
        }
    }

    @Test
    @DisplayName("卡有效期处理 - 从14号域获取")
    void testBuildAuthRecorded_CardExpirationFromField14() throws IOException {
        // Given
        fieldMap.put(Constants8583Field.FIELD14, "2512");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("2512", result.getAuthCardExpirationDate());
        }
    }

    @Test
    @DisplayName("卡有效期处理 - 从35号域获取")
    void testBuildAuthRecorded_CardExpirationFromField35() throws IOException {
        // Given
        fieldMap.remove(Constants8583Field.FIELD14);
        fieldMap.put(Constants8583Field.FIELD35, "1234567890123456=25121234567890123456");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("2512", result.getAuthCardExpirationDate());
        }
    }

    @Test
    @DisplayName("卡有效期处理 - 从45号域获取")
    void testBuildAuthRecorded_CardExpirationFromField45() throws IOException {
        // Given
        fieldMap.remove(Constants8583Field.FIELD14);
        fieldMap.put(Constants8583Field.FIELD45, "TESTCARD^HOLDER^2512123456789");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("^251", result.getAuthCardExpirationDate());
        }
    }

    @Test
    @DisplayName("54号域处理 - 长度大于等于20")
    void testBuildAuthRecorded_Field54LongLength() throws IOException {
        // Given
        fieldMap.put(Constants8583Field.FIELD54, "12345678901234567890123456789012");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("901234567890", result.getAuthAdditionalAmounts());
        }
    }

    @Test
    @DisplayName("54号域处理 - 长度小于20")
    void testBuildAuthRecorded_Field54ShortLength() throws IOException {
        // Given
        fieldMap.put(Constants8583Field.FIELD54, "1234567890");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertEquals("1234567890", result.getAuthAdditionalAmounts());
        }
    }

    @Test
    @DisplayName("跳过交易类型构建 - 18开头MTI")
    void testBuildTransType_Skip18MTI() throws IOException {
        // Given
        iso8583DTO.setMTI("1800");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertNull(result.getAuthReversalType());
            assertNull(result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("跳过交易类型构建 - 16开头MTI")
    void testBuildTransType_Skip16MTI() throws IOException {
        // Given
        iso8583DTO.setMTI("1600");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertNull(result.getAuthReversalType());
            assertNull(result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("跳过交易类型构建 - 13开头MTI")
    void testBuildTransType_Skip13MTI() throws IOException {
        // Given
        iso8583DTO.setMTI("1300");

        try (MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertNull(result.getAuthReversalType());
            assertNull(result.getAuthTransactionTypeCode());
        }
    }

    @Test
    @DisplayName("ThreadLocal设置验证")
    void testThreadLocalSetting() throws IOException {
        // Given
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(transIdentifyMap);
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertNotNull(AuthThreadLocalManager.getIso8583dtoThreadLocal());
            assertNotNull(AuthThreadLocalManager.getAuthRecordeddtoThreadLocal());
            assertEquals(iso8583DTO, AuthThreadLocalManager.getIso8583dtoThreadLocal());
            assertEquals(result, AuthThreadLocalManager.getAuthRecordeddtoThreadLocal());
        }
    }

    @Test
    @DisplayName("异常场景 - IOException抛出")
    void testPreProcessAuthTrans_IOException() {
        // Given
        ISO8583DTO nullFieldMapDto = new ISO8583DTO();
        nullFieldMapDto.setMTI("1100");
        nullFieldMapDto.setFieldMap(null);

        // When & Then
        assertThrows(NullPointerException.class, () -> {
            dciAuthTransPreprocessService.preProcessAuthTrans(nullFieldMapDto);
        });
    }

    @Test
    @DisplayName("交易识别返回空结果")
    void testTransIdentify_EmptyResult() throws IOException {
        // Given
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any(ISO8583DTO.class)))
                    .thenReturn(Collections.emptyMap());
            when(sequenceIdGen.generateId(anyString())).thenReturn("123456789");
            when(parmCurrencyCodeSelfMapper.selectByCurrencyCode("702")).thenReturn(currencyCode);

            // When
            AuthRecordedDTO result = dciAuthTransPreprocessService.preProcessAuthTrans(iso8583DTO);

            // Then
            assertNotNull(result);
            assertNull(result.getAuthTransactionTypeTopCode());
            assertNull(result.getAuthTransactionTypeDetailCode());
            assertNull(result.getPostingTransactionCode());
            assertNull(result.getPostingTransactionCodeRev());
        }
    }
}
