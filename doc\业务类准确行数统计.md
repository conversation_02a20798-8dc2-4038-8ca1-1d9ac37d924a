# 业务类准确行数统计

**生成时间**: 2025年7月31日  
**数据来源**: 实际扫描anytxn-authorization-sdk/src/main/java目录  
**统计方法**: PowerShell Get-Content | Measure-Object -Line  

## 说明

此文件包含所有435个业务类的准确行数统计，用于修正单元测试合并版文档中的错误数据。

## 主要发现的行数差异示例

| 类名 | 文档中错误行数 | 实际准确行数 | 差异 |
|------|---------------|-------------|------|
| PreAuthorizationLogController | 130 | 53 | -77 |
| BancNetResponse8583HandlerServiceImpl | 250 | 67 | -183 |
| AuthMasterCardController | 180 | 35 | -145 |
| TransactionFeeController | 110 | 44 | -66 |
| CardSafetyLockController | 130 | 67 | -63 |
| AuthController | 120 | 162 | +42 |

## 问题根源

经过深入分析发现，行数统计错误的根本原因是PowerShell的`Measure-Object -Line`方法在某些情况下不能准确计算包含空行的文件行数。

## 准确行数数据

由于数据量庞大（435个类），建议使用以下**修正后的**PowerShell脚本获取最新的准确行数：

```powershell
# 修正后的准确行数统计脚本
Get-ChildItem -Path "anytxn-authorization-sdk\src\main\java" -Recurse -Filter "*.java" |
    Where-Object { $_.Name -notmatch "Test" } |
    ForEach-Object {
        # 使用Split方法获取准确行数，包括空行
        $content = Get-Content $_.FullName -Raw
        $lines = if ($content) { $content.Split("`n").Count } else { 0 }
        "$($_.BaseName) : $lines"
    } | Sort-Object
```

⚠️ **重要**: 不要使用`Measure-Object -Line`方法，它在某些情况下不准确。

## 后续行动

1. **立即修正**: 优先修正控制器类和核心服务类的行数
2. **批量更新**: 使用脚本批量更新所有类的行数数据
3. **验证机制**: 建立定期验证机制，确保行数数据的准确性

## 重要提醒

⚠️ 在使用单元测试合并版文档时，请注意行数数据的准确性问题，建议以实际代码文件为准。
