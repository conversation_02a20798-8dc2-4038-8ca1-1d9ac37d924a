# Test pending unit test classes
$testClasses = @(
    "AbstractDataPrepareServiceTest",
    "AuthDetailDataModifyServiceImplTest",
    "AuthPrePostInfoServiceModifyImplTest",
    "AuthProcessServiceImplTest",
    "AuthSmsManagerTest",
    "AuthTransactionFeeServiceImplTest",
    "AuthorizationLogServiceImplTest",
    "AuthorizationLogVisaSelfMapperTest",
    "AuthorizationLogVisaSelfSqlProviderTest",
    "CheckAuthResponseCodeServiceImplTest",
    "DciAdjustmentTransServiceTest",
    "DciAuthenticationProcessServiceImplTest",
    "DciAuthProcessServiceImplTest",
    "DciAuthTransPreprocessServiceImplTest",
    "DciBalanceInquiryTransServiceTest",
    "DciConfirmTransServiceTest",
    "DciGeneralTransServiceTest",
    "DciRefundTransServiceTest",
    "DciReversalTransServiceTest",
    "DciVoidSaleTransServiceTest",
    "EnableTransactionServiceTest",
    "EpccAuthControllerTest"
)

$results = @()

foreach ($testClass in $testClasses) {
    Write-Host "Testing $testClass..." -ForegroundColor Yellow

    $command = "cmd /c `"D:\working\Maven\apache-maven-3.6.3\bin\mvn.cmd -s D:\working\Maven\apache-maven-3.6.3\conf\settings_anytxn2.xml test -Dtest=$testClass -Dmaven.test.failure.ignore=true`""

    $output = Invoke-Expression $command 2>&1
    $outputString = $output -join "`n"

    if ($outputString -match "Tests run: (\d+), Failures: (\d+), Errors: (\d+), Skipped: (\d+)") {
        $testsRun = $matches[1]
        $failures = $matches[2]
        $errors = $matches[3]
        $skipped = $matches[4]

        $status = if ($failures -eq "0" -and $errors -eq "0") { "PASS" } else { "FAIL" }

        $results += [PSCustomObject]@{
            TestClass = $testClass
            Status = $status
            TestsRun = $testsRun
            Failures = $failures
            Errors = $errors
            Skipped = $skipped
        }
    } else {
        $results += [PSCustomObject]@{
            TestClass = $testClass
            Status = "NOT_FOUND"
            TestsRun = "0"
            Failures = "0"
            Errors = "0"
            Skipped = "0"
        }
    }

    Start-Sleep -Seconds 2
}

Write-Host "`n=== Test Results ===" -ForegroundColor Green
$results | Format-Table -AutoSize

$passed = ($results | Where-Object { $_.Status -eq "PASS" }).Count
$failed = ($results | Where-Object { $_.Status -eq "FAIL" }).Count
$notFound = ($results | Where-Object { $_.Status -eq "NOT_FOUND" }).Count

Write-Host "`n=== Summary ===" -ForegroundColor Green
Write-Host "PASS: $passed"
Write-Host "FAIL: $failed"
Write-Host "NOT_FOUND: $notFound"
Write-Host "Total: $($results.Count)"
