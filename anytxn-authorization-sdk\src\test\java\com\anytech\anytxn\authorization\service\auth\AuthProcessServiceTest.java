package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @description AuthProcessService接口的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthProcessService 授权处理服务接口测试")
class AuthProcessServiceTest {

    @Mock
    private AuthProcessService authProcessService;

    private AuthRecordedDTO authRecordedDTO;
    private ISO8583DTO iso8583DTO;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        when(OrgNumberUtils.getOrg()).thenReturn("001");
        when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 创建授权流水DTO
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("1000"));
        // authRecordedDTO.setAuthMerchantNumber("123456789012345"); // 字段可能不存在

        // 创建8583DTO
        iso8583DTO = new ISO8583DTO();
        // 由于setMsgType可能不存在，我们只创建对象
    }

    @Test
    @DisplayName("测试execute方法-正常执行")
    void testExecute_NormalExecution() {
        // Arrange
        doNothing().when(authProcessService).execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));

        // Act
        assertDoesNotThrow(() -> {
            authProcessService.execute(authRecordedDTO, iso8583DTO);
        });

        // Assert
        verify(authProcessService).execute(eq(authRecordedDTO), eq(iso8583DTO));
    }

    @Test
    @DisplayName("测试execute方法-空AuthRecordedDTO参数")
    void testExecute_NullAuthRecordedDTO() {
        // Arrange
        doNothing().when(authProcessService).execute(isNull(), any(ISO8583DTO.class));

        // Act
        assertDoesNotThrow(() -> {
            authProcessService.execute(null, iso8583DTO);
        });

        // Assert
        verify(authProcessService).execute(isNull(), eq(iso8583DTO));
    }

    @Test
    @DisplayName("测试execute方法-空ISO8583DTO参数")
    void testExecute_NullISO8583DTO() {
        // Arrange
        doNothing().when(authProcessService).execute(any(AuthRecordedDTO.class), isNull());

        // Act
        assertDoesNotThrow(() -> {
            authProcessService.execute(authRecordedDTO, null);
        });

        // Assert
        verify(authProcessService).execute(eq(authRecordedDTO), isNull());
    }

    @Test
    @DisplayName("测试execute方法-两个参数都为空")
    void testExecute_BothParametersNull() {
        // Arrange
        doNothing().when(authProcessService).execute(isNull(), isNull());

        // Act
        assertDoesNotThrow(() -> {
            authProcessService.execute(null, null);
        });

        // Assert
        verify(authProcessService).execute(isNull(), isNull());
    }

    @Test
    @DisplayName("测试execute方法-抛出异常")
    void testExecute_ThrowsException() {
        // Arrange
        doThrow(new RuntimeException("授权处理异常")).when(authProcessService)
                .execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            authProcessService.execute(authRecordedDTO, iso8583DTO);
        });

        verify(authProcessService).execute(eq(authRecordedDTO), eq(iso8583DTO));
    }

    @Test
    @DisplayName("测试execute方法-多次调用")
    void testExecute_MultipleCalls() {
        // Arrange
        doNothing().when(authProcessService).execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));

        // Act
        authProcessService.execute(authRecordedDTO, iso8583DTO);
        authProcessService.execute(authRecordedDTO, iso8583DTO);
        authProcessService.execute(authRecordedDTO, iso8583DTO);

        // Assert
        verify(authProcessService, times(3)).execute(eq(authRecordedDTO), eq(iso8583DTO));
    }

    @Test
    @DisplayName("测试execute方法-不同参数调用")
    void testExecute_DifferentParameters() {
        // Arrange
        AuthRecordedDTO anotherAuthDTO = new AuthRecordedDTO();
        anotherAuthDTO.setAuthCardNumber("9876543210987654");
        anotherAuthDTO.setAuthTransactionAmount(new BigDecimal("2000"));
        
        ISO8583DTO anotherIso8583DTO = new ISO8583DTO();
        
        doNothing().when(authProcessService).execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));

        // Act
        authProcessService.execute(authRecordedDTO, iso8583DTO);
        authProcessService.execute(anotherAuthDTO, anotherIso8583DTO);

        // Assert
        verify(authProcessService).execute(eq(authRecordedDTO), eq(iso8583DTO));
        verify(authProcessService).execute(eq(anotherAuthDTO), eq(anotherIso8583DTO));
        verify(authProcessService, times(2)).execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));
    }

    @Test
    @DisplayName("测试execute方法-验证方法签名")
    void testExecute_MethodSignature() {
        // Arrange
        doNothing().when(authProcessService).execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));

        // Act
        authProcessService.execute(authRecordedDTO, iso8583DTO);

        // Assert
        // 验证方法被正确调用，参数类型正确
        verify(authProcessService).execute(
                argThat(dto -> dto != null && dto.getAuthCardNumber() != null),
                argThat(iso -> iso != null)
        );
    }

    @Test
    @DisplayName("测试execute方法-验证无返回值")
    void testExecute_VoidReturn() {
        // Arrange
        doNothing().when(authProcessService).execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));

        // Act
        // execute方法应该没有返回值
        authProcessService.execute(authRecordedDTO, iso8583DTO);

        // Assert
        // 验证方法被调用且没有返回值
        verify(authProcessService).execute(eq(authRecordedDTO), eq(iso8583DTO));
        // 由于是void方法，我们只能验证调用而不能验证返回值
    }

    @Test
    @DisplayName("测试execute方法-验证参数传递")
    void testExecute_ParameterPassing() {
        // Arrange
        doNothing().when(authProcessService).execute(any(AuthRecordedDTO.class), any(ISO8583DTO.class));

        // Act
        authProcessService.execute(authRecordedDTO, iso8583DTO);

        // Assert
        // 验证传递的参数对象是正确的
        verify(authProcessService).execute(
                argThat(dto -> dto.equals(authRecordedDTO)),
                argThat(iso -> iso.equals(iso8583DTO))
        );
    }
} 