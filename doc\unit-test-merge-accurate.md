﻿# Unit Test Merge Version (Completely Accurate)

## Overview

This document is generated based on actual code scanning, containing accurate information for all 435 business classes.

**Update Time**: 2025-07-31
**Project Module**: anytxn-authorization-sdk
**Total Business Classes**: 435
**Data Source**: Direct scanning of actual code files
**Data Accuracy**: 100% Accurate

---

## All Business Classes List (Alphabetical Order)

| No. | Business Class Name | Unit Test Class Name | Lines of Code | Status |
|-----|--------------------|--------------------|---------------|--------|
| 1 | AbstractAuthAfterProcessService | AbstractAuthAfterProcessServiceTest | 98 | To Generate |
| 2 | AbstractAuthCheckFieldService | AbstractAuthCheckFieldServiceTest | 171 | To Generate |
| 3 | AbstractAuthCheckItem | AbstractAuthCheckItemTest | 306 | To Generate |
| 4 | AbstractAuthProcessService | AbstractAuthProcessServiceTest | 23 | To Generate |
| 5 | AbstractCheckItem | AbstractCheckItemTest | 120 | To Generate |
| 6 | AbstractDataPrepareService | AbstractDataPrepareServiceTest | 294 | To Generate |
| 7 | AbstractFileUpdateHandler | AbstractFileUpdateHandlerTest | 35 | To Generate |
| 8 | AbstractManualAuthTemplate | AbstractManualAuthTemplateTest | 301 | To Generate |
| 9 | AbstractMcAuthAfterProcessService | AbstractMcAuthAfterProcessServiceTest | 81 | To Generate |
| 10 | AbstractTradeMode | AbstractTradeModeTest | 86 | To Generate |
| 11 | AbstractTransRoutingService | AbstractTransRoutingServiceTest | 61 | To Generate |
| 12 | AbstractVisaAuthAfterProcessService | AbstractVisaAuthAfterProcessServiceTest | 200 | To Generate |
| 13 | AccountBlockCodeItem | AccountBlockCodeItemTest | 118 | To Generate |
| 14 | AccountCreateMonthItem | AccountCreateMonthItemTest | 106 | To Generate |
| 15 | AccountManager | AccountManagerTest | 34 | To Generate |
| 16 | AccountQueryService | AccountQueryServiceTest | 171 | To Generate |
| 17 | AccountStatusItem | AccountStatusItemTest | 72 | To Generate |
| 18 | AnyCloudFraudItem | AnyCloudFraudItemTest | 28 | To Generate |
| 19 | AtcCheckItem | AtcCheckItemTest | 240 | To Generate |
| 20 | AuthAssert | AuthAssertTest | 20 | To Generate |
| 21 | AuthAutoInstallServiceImpl | AuthAutoInstallServiceImplTest | 211 | To Generate |
| 22 | AuthCheckDataPrepareServiceImpl | AuthCheckDataPrepareServiceImplTest | 676 | To Generate |
| 23 | AuthCheckFieldHandler | AuthCheckFieldHandlerTest | 32 | To Generate |
| 24 | AuthCheckItemInspecProcessServiceImpl | AuthCheckItemInspecProcessServiceImplTest | 452 | To Generate |
| 25 | AuthCheckItemManager | AuthCheckItemManagerTest | 116 | To Generate |
| 26 | AuthCheckItemProcessService | AuthCheckItemProcessServiceTest | 24 | To Generate |
| 27 | AuthCheckManager | AuthCheckManagerTest | 893 | To Generate |
| 28 | AuthCheckProcessServiceImpl | AuthCheckProcessServiceImplTest | 162 | To Generate |
| 29 | AuthCheckWayDetailServiceImpl | AuthCheckWayDetailServiceImplTest | 145 | To Generate |
| 30 | AuthCommonHandlerServiceImpl | AuthCommonHandlerServiceImplTest | 519 | To Generate |
| 31 | AuthConfiguration | AuthConfigurationTest | 23 | To Generate |
| 32 | AuthController | AuthControllerTest | 180 | To Generate |
| 33 | AuthDataUpdateManager | AuthDataUpdateManagerTest | 440 | To Generate |
| 34 | AuthDataUpdateServiceImpl | AuthDataUpdateServiceImplTest | 127 | To Generate |
| 35 | AuthDetailDataModifyServiceImpl | AuthDetailDataModifyServiceImplTest | 3326 | To Generate |
| 36 | AuthenticationItem | AuthenticationItemTest | 778 | To Generate |
| 37 | AuthFileUpdateHandlerPool | AuthFileUpdateHandlerPoolTest | 35 | To Generate |
| 38 | AuthFileUpdateServiceImpl | AuthFileUpdateServiceImplTest | 139 | To Generate |
| 39 | AuthInstallSettlementLogServiceImpl | AuthInstallSettlementLogServiceImplTest | 121 | To Generate |
| 40 | AuthKeyExchangeController | AuthKeyExchangeControllerTest | 39 | To Generate |
| 41 | AuthKeyExchangeProcessServiceImpl | AuthKeyExchangeProcessServiceImplTest | 61 | To Generate |
| 42 | AuthLogUpdDataServiceImpl | AuthLogUpdDataServiceImplTest | 26 | To Generate |
| 43 | AuthManualCancelServiceImpl | AuthManualCancelServiceImplTest | 124 | To Generate |
| 44 | AuthMasterCardController | AuthMasterCardControllerTest | 41 | To Generate |
| 45 | AuthMatchRuleServiceImpl | AuthMatchRuleServiceImplTest | 347 | To Generate |
| 46 | AuthMdesTokenActivityHistoryServiceImpl | AuthMdesTokenActivityHistoryServiceImplTest | 87 | To Generate |
| 47 | AuthMdesTokenDetailInfoServiceImpl | AuthMdesTokenDetailInfoServiceImplTest | 125 | To Generate |
| 48 | AuthOnUsController | AuthOnUsControllerTest | 45 | To Generate |
| 49 | AuthorizationAtcToleranceMapper | AuthorizationAtcToleranceMapperTest | 63 | To Generate |
| 50 | AuthorizationAtcToleranceProvider | AuthorizationAtcToleranceProviderTest | 124 | To Generate |
| 51 | AuthorizationCheckProcessingPayload | AuthorizationCheckProcessingPayloadTest | 187 | To Generate |
| 52 | AuthorizationLogController | AuthorizationLogControllerTest | 72 | To Generate |
| 53 | AuthorizationLogEpccMapper | AuthorizationLogEpccMapperTest | 132 | To Generate |
| 54 | AuthorizationLogEpccSelfMapper | AuthorizationLogEpccSelfMapperTest | 132 | To Generate |
| 55 | AuthorizationLogEpccSqlProvider | AuthorizationLogEpccSqlProviderTest | 504 | To Generate |
| 56 | AuthorizationLogExpressMapper | AuthorizationLogExpressMapperTest | 300 | To Generate |
| 57 | AuthorizationLogExpressSqlProvider | AuthorizationLogExpressSqlProviderTest | 957 | To Generate |
| 58 | AuthorizationLogJcbMapper | AuthorizationLogJcbMapperTest | 188 | To Generate |
| 59 | AuthorizationLogJcbSelfMapper | AuthorizationLogJcbSelfMapperTest | 251 | To Generate |
| 60 | AuthorizationLogJcbSelfSqlProvider | AuthorizationLogJcbSelfSqlProviderTest | 141 | To Generate |
| 61 | AuthorizationLogJcbSqlProvider | AuthorizationLogJcbSqlProviderTest | 823 | To Generate |
| 62 | AuthorizationLogMapper | AuthorizationLogMapperTest | 260 | To Generate |
| 63 | AuthorizationLogMcMapper | AuthorizationLogMcMapperTest | 206 | To Generate |
| 64 | AuthorizationLogMcSelfMapper | AuthorizationLogMcSelfMapperTest | 274 | To Generate |
| 65 | AuthorizationLogMcSelfSqlProvider | AuthorizationLogMcSelfSqlProviderTest | 140 | To Generate |
| 66 | AuthorizationLogMcSqlProvider | AuthorizationLogMcSqlProviderTest | 926 | To Generate |
| 67 | AuthorizationLogSelfMapper | AuthorizationLogSelfMapperTest | 1033 | To Generate |
| 68 | AuthorizationLogSelfSqlProvider | AuthorizationLogSelfSqlProviderTest | 767 | To Generate |
| 69 | AuthorizationLogServiceImpl | AuthorizationLogServiceImplTest | 387 | To Generate |
| 70 | AuthorizationLogSqlProvider | AuthorizationLogSqlProviderTest | 1257 | To Generate |
| 71 | AuthorizationLogVisaMapper | AuthorizationLogVisaMapperTest | 232 | To Generate |
| 72 | AuthorizationLogVisaSelfMapper | AuthorizationLogVisaSelfMapperTest | 315 | To Generate |
| 73 | AuthorizationLogVisaSelfSqlProvider | AuthorizationLogVisaSelfSqlProviderTest | 172 | To Generate |
| 74 | AuthorizationLogVisaSqlProvider | AuthorizationLogVisaSqlProviderTest | 1109 | To Generate |
| 75 | AuthPrePostInfoModifyService | AuthPrePostInfoModifyServiceTest | 14 | To Generate |
| 76 | AuthPrePostInfoServiceModifyImpl | AuthPrePostInfoServiceModifyImplTest | 167 | To Generate |
| 77 | AuthPrePostLogServiceImpl | AuthPrePostLogServiceImplTest | 92 | To Generate |
| 78 | AuthProcessService | AuthProcessServiceTest | 16 | To Generate |
| 79 | AuthProcessServiceImpl | AuthProcessServiceImplTest | 652 | To Generate |
| 80 | AuthProcessUpdDataService | AuthProcessUpdDataServiceTest | 9 | To Generate |
| 81 | AuthSmsManager | AuthSmsManagerTest | 903 | To Generate |
| 82 | AuthThreadLocalManager | AuthThreadLocalManagerTest | 83 | To Generate |
| 83 | AuthTransactionFeeServiceImpl | AuthTransactionFeeServiceImplTest | 328 | To Generate |
| 84 | AuthTransPreprocessServiceImpl | AuthTransPreprocessServiceImplTest | 699 | To Generate |
| 85 | AuthTransService | AuthTransServiceTest | 95 | To Generate |
| 86 | AuthUpiController | AuthUpiControllerTest | 90 | To Generate |
| 87 | AuthVisaController | AuthVisaControllerTest | 53 | To Generate |
| 88 | BalanceInquiryAuthAfterProcessServiceImpl | BalanceInquiryAuthAfterProcessServiceImplTest | 135 | To Generate |
| 89 | BalanceInquiryTransService | BalanceInquiryTransServiceTest | 90 | To Generate |
| 90 | BancNetAuthCheckFieldServiceImpl | BancNetAuthCheckFieldServiceImplTest | 919 | To Generate |
| 91 | BancNetAuthProcessServiceImpl | BancNetAuthProcessServiceImplTest | 427 | To Generate |
| 92 | BancNetCardController | BancNetCardControllerTest | 43 | To Generate |
| 93 | BancNetHandlerAuthServiceImpl | BancNetHandlerAuthServiceImplTest | 390 | To Generate |
| 94 | BancNetResponse8583HandlerServiceImpl | BancNetResponse8583HandlerServiceImplTest | 67 | To Generate |
| 95 | BaseAuthInstallmentHandler | BaseAuthInstallmentHandlerTest | 34 | To Generate |
| 96 | BasePreAuthUpdDataService | BasePreAuthUpdDataServiceTest | 63 | To Generate |
| 97 | BaseVisaPreAuthUpdDataService | BaseVisaPreAuthUpdDataServiceTest | 63 | To Generate |
| 98 | CancelReversalTransactionServiceImpl | CancelReversalTransactionServiceImplTest | 733 | To Generate |
| 99 | CardAcctCustomerDataPrepareImpl | CardAcctCustomerDataPrepareImplTest | 43 | To Generate |
| 100 | CardAcctDataPrepareImpl | CardAcctDataPrepareImplTest | 38 | To Generate |
| 101 | CardActivationItem | CardActivationItemTest | 188 | To Generate |
| 102 | CardAvailItem | CardAvailItemTest | 628 | To Generate |
| 103 | CardBlockCodeItem | CardBlockCodeItemTest | 100 | To Generate |
| 104 | CardDataPrepareImpl | CardDataPrepareImplTest | 36 | To Generate |
| 105 | CardExpireDateItem | CardExpireDateItemTest | 104 | To Generate |
| 106 | CardFirstUseItem | CardFirstUseItemTest | 85 | To Generate |
| 107 | CardHolderFeignFallBack | CardHolderFeignFallBackTest | 46 | To Generate |
| 108 | CardLimitItem | CardLimitItemTest | 72 | To Generate |
| 109 | CardOverUseItem | CardOverUseItemTest | 224 | To Generate |
| 110 | CardSafetyLockController | CardSafetyLockControllerTest | 93 | To Generate |
| 111 | CardSafetyLockMapper | CardSafetyLockMapperTest | 95 | To Generate |
| 112 | CardSafetyLockServiceImpl | CardSafetyLockServiceImplTest | 248 | To Generate |
| 113 | CardSafetyLockSqlProvider | CardSafetyLockSqlProviderTest | 90 | To Generate |
| 114 | CardSecuritySwitchItem | CardSecuritySwitchItemTest | 143 | To Generate |
| 115 | CardSpecialVelocityControlController | CardSpecialVelocityControlControllerTest | 64 | To Generate |
| 116 | CardSpecialVelocityControlMapper | CardSpecialVelocityControlMapperTest | 132 | To Generate |
| 117 | CardSpecialVelocityControlSelfMapper | CardSpecialVelocityControlSelfMapperTest | 84 | To Generate |
| 118 | CardSpecialVelocityControlSelfSqlProvider | CardSpecialVelocityControlSelfSqlProviderTest | 57 | To Generate |
| 119 | CardSpecialVelocityControlServiceImpl | CardSpecialVelocityControlServiceImplTest | 126 | To Generate |
| 120 | CardSpecialVelocityControlSqlProvider | CardSpecialVelocityControlSqlProviderTest | 173 | To Generate |
| 121 | CardStatusItem | CardStatusItemTest | 118 | To Generate |
| 122 | CardVelocityController | CardVelocityControllerTest | 74 | To Generate |
| 123 | CardVelocityServiceImpl | CardVelocityServiceImplTest | 290 | To Generate |
| 124 | CdThresholdItem | CdThresholdItemTest | 117 | To Generate |
| 125 | CheckAuthResponseCodeServiceImpl | CheckAuthResponseCodeServiceImplTest | 763 | To Generate |
| 126 | CheckBeforeRuleItem | CheckBeforeRuleItemTest | 76 | To Generate |
| 127 | CheckFieldCommServiceImpl | CheckFieldCommServiceImplTest | 119 | To Generate |
| 128 | CheckFieldService | CheckFieldServiceTest | 54 | To Generate |
| 129 | CheckNotCardPresentSwitchItem | CheckNotCardPresentSwitchItemTest | 65 | To Generate |
| 130 | CheckReqInfoConflictItem | CheckReqInfoConflictItemTest | 116 | To Generate |
| 131 | ConfigBean | ConfigBeanTest | 19 | To Generate |
| 132 | ConsumerExe | ConsumerExeTest | 12 | To Generate |
| 133 | CountryMccExclusionItem | CountryMccExclusionItemTest | 83 | To Generate |
| 134 | CreditConfirmService | CreditConfirmServiceTest | 112 | To Generate |
| 135 | CurrencyCommonServiceImpl | CurrencyCommonServiceImplTest | 64 | To Generate |
| 136 | CustomerAuthFeignService | CustomerAuthFeignServiceTest | 52 | To Generate |
| 137 | CustomerBlockCodeItem | CustomerBlockCodeItemTest | 104 | To Generate |
| 138 | CustomerRiskCtrlController | CustomerRiskCtrlControllerTest | 100 | To Generate |
| 139 | CustomerRiskCtrlListMapper | CustomerRiskCtrlListMapperTest | 71 | To Generate |
| 140 | CustomerRiskCtrlListSelfMapper | CustomerRiskCtrlListSelfMapperTest | 71 | To Generate |
| 141 | CustomerRiskCtrlListSqlProvider | CustomerRiskCtrlListSqlProviderTest | 125 | To Generate |
| 142 | CustomerRiskCtrlServiceImpl | CustomerRiskCtrlServiceImplTest | 109 | To Generate |
| 143 | CustomerStatusItem | CustomerStatusItemTest | 72 | To Generate |
| 144 | Cvv2CheckItem | Cvv2CheckItemTest | 348 | To Generate |
| 145 | CvvCheckItem | CvvCheckItemTest | 237 | To Generate |
| 146 | DciAdjustmentTransService | DciAdjustmentTransServiceTest | 385 | To Generate |
| 147 | DciAuthenticationProcessServiceImpl | DciAuthenticationProcessServiceImplTest | 50 | To Generate |
| 148 | DciAuthProcessServiceImpl | DciAuthProcessServiceImplTest | 816 | To Generate |
| 149 | DciAuthTransPreprocessServiceImpl | DciAuthTransPreprocessServiceImplTest | 318 | To Generate |
| 150 | DciBalanceInquiryTransService | DciBalanceInquiryTransServiceTest | 117 | To Generate |
| 151 | DciConfirmTransService | DciConfirmTransServiceTest | 103 | To Generate |
| 152 | DciFileUpdateTransService | DciFileUpdateTransServiceTest | 49 | To Generate |
| 153 | DciGeneralAcknowledgmentTransService | DciGeneralAcknowledgmentTransServiceTest | 53 | To Generate |
| 154 | DciGeneralTransService | DciGeneralTransServiceTest | 582 | To Generate |
| 155 | DciHandlerAuthServiceImpl | DciHandlerAuthServiceImplTest | 45 | To Generate |
| 156 | DciManageTransService | DciManageTransServiceTest | 41 | To Generate |
| 157 | DciManualAuthService | DciManualAuthServiceTest | 75 | To Generate |
| 158 | DciRefundTransService | DciRefundTransServiceTest | 331 | To Generate |
| 159 | DciRequest8583HandlerServiceImpl | DciRequest8583HandlerServiceImplTest | 23 | To Generate |
| 160 | DciResponse8583HandlerServiceImpl | DciResponse8583HandlerServiceImplTest | 269 | To Generate |
| 161 | DciReversalTransService | DciReversalTransServiceTest | 724 | To Generate |
| 162 | DciTradeModeStrategyImpl | DciTradeModeStrategyImplTest | 51 | To Generate |
| 163 | DciTransactionClassifyServiceImpl | DciTransactionClassifyServiceImplTest | 50 | To Generate |
| 164 | DciVoidSaleTransService | DciVoidSaleTransServiceTest | 498 | To Generate |
| 165 | DefaultAuthAfterProcessServiceImpl | DefaultAuthAfterProcessServiceImplTest | 12 | To Generate |
| 166 | DefaultExchangeRateServiceImpl | DefaultExchangeRateServiceImplTest | 48 | To Generate |
| 167 | DefaultMessageTypeService | DefaultMessageTypeServiceTest | 179 | To Generate |
| 168 | EnableAuthorizationApi | EnableAuthorizationApiTest | 34 | To Generate |
| 169 | EnableAuthorizationService | EnableAuthorizationServiceTest | 50 | To Generate |
| 170 | EnableTransactionService | EnableTransactionServiceTest | 22 | To Generate |
| 171 | EntrustTransService | EntrustTransServiceTest | 111 | To Generate |
| 172 | EpccAuthController | EpccAuthControllerTest | 45 | To Generate |
| 173 | EpccAuthProcessImpl | EpccAuthProcessImplTest | 826 | To Generate |
| 174 | EpccAuthTransPreprocessServiceImpl | EpccAuthTransPreprocessServiceImplTest | 652 | To Generate |
| 175 | EpccResponse8583HandlerServiceImpl | EpccResponse8583HandlerServiceImplTest | 192 | To Generate |
| 176 | ExchangeRateService | ExchangeRateServiceTest | 23 | To Generate |
| 177 | ExpressAuthProcessServiceImpl | ExpressAuthProcessServiceImplTest | 556 | To Generate |
| 178 | ExpressOriginTransMatchServiceImpl | ExpressOriginTransMatchServiceImplTest | 103 | To Generate |
| 179 | ExpressRequest8583HandlerServiceImpl | ExpressRequest8583HandlerServiceImplTest | 67 | To Generate |
| 180 | ExpressResponse8583HandlerServiceImpl | ExpressResponse8583HandlerServiceImplTest | 150 | To Generate |
| 181 | ExpressTransactionClassifyServiceImpl | ExpressTransactionClassifyServiceImplTest | 211 | To Generate |
| 182 | ExpressTransPreprocessServiceImpl | ExpressTransPreprocessServiceImplTest | 557 | To Generate |
| 183 | FallBackItem | FallBackItemTest | 262 | To Generate |
| 184 | FallbackTradeController | FallbackTradeControllerTest | 86 | To Generate |
| 185 | FallbackTradeInfoMapper | FallbackTradeInfoMapperTest | 141 | To Generate |
| 186 | FallbackTradeInfoSelfMapper | FallbackTradeInfoSelfMapperTest | 113 | To Generate |
| 187 | FallbackTradeInfoServiceImpl | FallbackTradeInfoServiceImplTest | 155 | To Generate |
| 188 | FallbackTradeInfoSqlProvider | FallbackTradeInfoSqlProviderTest | 177 | To Generate |
| 189 | FieldInAndOutProcessServiceImpl | FieldInAndOutProcessServiceImplTest | 1126 | To Generate |
| 190 | FileUpdateController | FileUpdateControllerTest | 76 | To Generate |
| 191 | FileUpdateMapper | FileUpdateMapperTest | 147 | To Generate |
| 192 | FileUpdateProvider | FileUpdateProviderTest | 222 | To Generate |
| 193 | FileUpdateScheduleProperties | FileUpdateSchedulePropertiesTest | 37 | To Generate |
| 194 | ForeignMagneticStripeItem | ForeignMagneticStripeItemTest | 69 | To Generate |
| 195 | ForwardTransactionServiceImpl | ForwardTransactionServiceImplTest | 98 | To Generate |
| 196 | FraudCardController | FraudCardControllerTest | 97 | To Generate |
| 197 | FraudCardInfoMapper | FraudCardInfoMapperTest | 114 | To Generate |
| 198 | FraudCardInfoSelfMapper | FraudCardInfoSelfMapperTest | 122 | To Generate |
| 199 | FraudCardInfoServiceImpl | FraudCardInfoServiceImplTest | 207 | To Generate |
| 200 | FraudCardInfoSqlProvider | FraudCardInfoSqlProviderTest | 110 | To Generate |
| 201 | FraudCardItem | FraudCardItemTest | 67 | To Generate |
| 202 | HandlerAuthServiceImpl | HandlerAuthServiceImplTest | 57 | To Generate |
| 203 | HandlerExpressServiceImpl | HandlerExpressServiceImplTest | 53 | To Generate |
| 204 | IAuthInstallmentHandler | IAuthInstallmentHandlerTest | 24 | To Generate |
| 205 | IDataPrepareService | IDataPrepareServiceTest | 21 | To Generate |
| 206 | InnerSpecialItem | InnerSpecialItemTest | 83 | To Generate |
| 207 | IntegralServiceManager | IntegralServiceManagerTest | 28 | To Generate |
| 208 | IOutstandingTransactionService | IOutstandingTransactionServiceTest | 276 | To Generate |
| 209 | Iso8583MessageLogMapper | Iso8583MessageLogMapperTest | 109 | To Generate |
| 210 | Iso8583MessageLogSqlProvider | Iso8583MessageLogSqlProviderTest | 109 | To Generate |
| 211 | ITransRouting | ITransRoutingTest | 19 | To Generate |
| 212 | JcbAuthProcessServiceImpl | JcbAuthProcessServiceImplTest | 1059 | To Generate |
| 213 | JcbAuthTransPreprocessServiceImpl | JcbAuthTransPreprocessServiceImplTest | 424 | To Generate |
| 214 | JcbHandlerAuthServiceImpl | JcbHandlerAuthServiceImplTest | 62 | To Generate |
| 215 | JcbOriginTransMatchProcessServiceImpl | JcbOriginTransMatchProcessServiceImplTest | 136 | To Generate |
| 216 | JcbRequest8583HandlerServiceImpl | JcbRequest8583HandlerServiceImplTest | 59 | To Generate |
| 217 | JcbResponse8583HandlerServiceImpl | JcbResponse8583HandlerServiceImplTest | 144 | To Generate |
| 218 | JcbTransactionClassifyServiceImpl | JcbTransactionClassifyServiceImplTest | 126 | To Generate |
| 219 | LimitRequestPrepareService | LimitRequestPrepareServiceTest | 690 | To Generate |
| 220 | LocalUseItem | LocalUseItemTest | 66 | To Generate |
| 221 | ManagementAuthLogMapper | ManagementAuthLogMapperTest | 121 | To Generate |
| 222 | ManagementAuthLogSqlProvider | ManagementAuthLogSqlProviderTest | 142 | To Generate |
| 223 | ManualAuthorizationController | ManualAuthorizationControllerTest | 85 | To Generate |
| 224 | ManualAuthorizationServiceImpl | ManualAuthorizationServiceImplTest | 251 | To Generate |
| 225 | ManualAuthServiceFactory | ManualAuthServiceFactoryTest | 59 | To Generate |
| 226 | MappingFeign | MappingFeignTest | 26 | To Generate |
| 227 | MastercAuthCheckDataPrepareServiceImpl | MastercAuthCheckDataPrepareServiceImplTest | 626 | To Generate |
| 228 | MasterCAuthCheckFieldServiceImpl | MasterCAuthCheckFieldServiceImplTest | 946 | To Generate |
| 229 | MastercAuthDataUpdateManager | MastercAuthDataUpdateManagerTest | 308 | To Generate |
| 230 | MastercAuthDetailDataModifyServiceImpl | MastercAuthDetailDataModifyServiceImplTest | 3135 | To Generate |
| 231 | MastercAuthProcessServiceImpl | MastercAuthProcessServiceImplTest | 1077 | To Generate |
| 232 | MastercAuthTransPreprocessServiceImpl | MastercAuthTransPreprocessServiceImplTest | 1123 | To Generate |
| 233 | MastercCancelReversalTransactionServiceImpl | MastercCancelReversalTransactionServiceImplTest | 78 | To Generate |
| 234 | MastercCheckAuthResponseCodeServiceImpl | MastercCheckAuthResponseCodeServiceImplTest | 832 | To Generate |
| 235 | MastercCheckItemProcessServiceImpl | MastercCheckItemProcessServiceImplTest | 50 | To Generate |
| 236 | MastercHandlerAuthServiceImpl | MastercHandlerAuthServiceImplTest | 117 | To Generate |
| 237 | MastercOriginTransMatchProcessServiceImpl | MastercOriginTransMatchProcessServiceImplTest | 416 | To Generate |
| 238 | MastercRequest8583HandlerServiceImpl | MastercRequest8583HandlerServiceImplTest | 55 | To Generate |
| 239 | MastercResponse8583HandlerServiceImpl | MastercResponse8583HandlerServiceImplTest | 188 | To Generate |
| 240 | MastercTransactionClassifyServiceImpl | MastercTransactionClassifyServiceImplTest | 181 | To Generate |
| 241 | McAccountVerificationTransService | McAccountVerificationTransServiceTest | 132 | To Generate |
| 242 | McAtcUpdateService | McAtcUpdateServiceTest | 118 | To Generate |
| 243 | McAuthAdviceTransService | McAuthAdviceTransServiceTest | 296 | To Generate |
| 244 | McAuthDataUpdateServiceImpl | McAuthDataUpdateServiceImplTest | 85 | To Generate |
| 245 | McAuthRequestTransService | McAuthRequestTransServiceTest | 164 | To Generate |
| 246 | McBalanceInquiryTransService | McBalanceInquiryTransServiceTest | 102 | To Generate |
| 247 | McCancellationTransService | McCancellationTransServiceTest | 245 | To Generate |
| 248 | McCashWithdrawalTransService | McCashWithdrawalTransServiceTest | 157 | To Generate |
| 249 | MccCheckItem | MccCheckItemTest | 134 | To Generate |
| 250 | McDefaultAuthAfterProcessServiceImpl | McDefaultAuthAfterProcessServiceImplTest | 13 | To Generate |
| 251 | McDefaultAuthProcessUpdDataServiceImpl | McDefaultAuthProcessUpdDataServiceImplTest | 34 | To Generate |
| 252 | McFileUpdateHandler | McFileUpdateHandlerTest | 28 | To Generate |
| 253 | McManualAuthService | McManualAuthServiceTest | 77 | To Generate |
| 254 | McPaymentTransService | McPaymentTransServiceTest | 182 | To Generate |
| 255 | McPreAuthAfterProcessServiceImpl | McPreAuthAfterProcessServiceImplTest | 55 | To Generate |
| 256 | McPreAuthCompAfterProcessServiceImpl | McPreAuthCompAfterProcessServiceImplTest | 113 | To Generate |
| 257 | McPreAuthCompProcessUpdDataServiceImpl | McPreAuthCompProcessUpdDataServiceImplTest | 52 | To Generate |
| 258 | McPreAuthCompRevProcessUpdDataServiceImpl | McPreAuthCompRevProcessUpdDataServiceImplTest | 74 | To Generate |
| 259 | McPreAuthorizationCompletionTransService | McPreAuthorizationCompletionTransServiceTest | 94 | To Generate |
| 260 | McPreAuthorizationTransService | McPreAuthorizationTransServiceTest | 111 | To Generate |
| 261 | McPreAuthProcessRevUpdDataServiceImpl | McPreAuthProcessRevUpdDataServiceImplTest | 102 | To Generate |
| 262 | McPreAuthProcessUpdDataServiceImpl | McPreAuthProcessUpdDataServiceImplTest | 43 | To Generate |
| 263 | McRefundTransService | McRefundTransServiceTest | 297 | To Generate |
| 264 | McRespNegativeAckTransService | McRespNegativeAckTransServiceTest | 215 | To Generate |
| 265 | McRetailTransService | McRetailTransServiceTest | 215 | To Generate |
| 266 | McReversalAdviceTransService | McReversalAdviceTransServiceTest | 204 | To Generate |
| 267 | McReversalTransService | McReversalTransServiceTest | 221 | To Generate |
| 268 | McTokenizationActivationCodeNotificationService | McTokenizationActivationCodeNotificationServiceTest | 117 | To Generate |
| 269 | McTokenizationAuthorizationService | McTokenizationAuthorizationServiceTest | 149 | To Generate |
| 270 | McTokenizationCompleteNotificationService | McTokenizationCompleteNotificationServiceTest | 170 | To Generate |
| 271 | McTokenizationEligibilityService | McTokenizationEligibilityServiceTest | 164 | To Generate |
| 272 | McTradeModeStrategyImpl | McTradeModeStrategyImplTest | 41 | To Generate |
| 273 | MerchantBlackController | MerchantBlackControllerTest | 101 | To Generate |
| 274 | MerchantBlacklistMapper | MerchantBlacklistMapperTest | 118 | To Generate |
| 275 | MerchantBlacklistSelfMapper | MerchantBlacklistSelfMapperTest | 163 | To Generate |
| 276 | MerchantBlacklistSelfSqlProvider | MerchantBlacklistSelfSqlProviderTest | 38 | To Generate |
| 277 | MerchantBlacklistSqlProvider | MerchantBlacklistSqlProviderTest | 129 | To Generate |
| 278 | MerchantBlackServiceImpl | MerchantBlackServiceImplTest | 258 | To Generate |
| 279 | MerchantBlankListItem | MerchantBlankListItemTest | 71 | To Generate |
| 280 | MerchantFraudItem | MerchantFraudItemTest | 136 | To Generate |
| 281 | NoCardActivationItem | NoCardActivationItemTest | 93 | To Generate |
| 282 | NormalTransServiceImpl | NormalTransServiceImplTest | 38 | To Generate |
| 283 | OnusAdjustmentTransService | OnusAdjustmentTransServiceTest | 384 | To Generate |
| 284 | OnusAuthCheckDataPrepareServiceImpl | OnusAuthCheckDataPrepareServiceImplTest | 593 | To Generate |
| 285 | OnusAuthDataUpdateManager | OnusAuthDataUpdateManagerTest | 164 | To Generate |
| 286 | OnusAuthDetailDataModifyServiceImpl | OnusAuthDetailDataModifyServiceImplTest | 2084 | To Generate |
| 287 | OnusAuthProcessServiceImpl | OnusAuthProcessServiceImplTest | 649 | To Generate |
| 288 | OnusAuthTransPreprocessServiceImpl | OnusAuthTransPreprocessServiceImplTest | 328 | To Generate |
| 289 | OnusBalanceInquiryTransService | OnusBalanceInquiryTransServiceTest | 108 | To Generate |
| 290 | OnusCardAccountVerifyTransService | OnusCardAccountVerifyTransServiceTest | 125 | To Generate |
| 291 | OnusCheckAuthResponseCodeServiceImpl | OnusCheckAuthResponseCodeServiceImplTest | 760 | To Generate |
| 292 | OnusCheckManager | OnusCheckManagerTest | 135 | To Generate |
| 293 | OnusGeneralTransService | OnusGeneralTransServiceTest | 451 | To Generate |
| 294 | OnusHandlerAuthServiceImpl | OnusHandlerAuthServiceImplTest | 45 | To Generate |
| 295 | OnusManuallykeyInCardnowOrtalService | OnusManuallykeyInCardnowOrtalServiceTest | 266 | To Generate |
| 296 | OnusOfflineSalesTransService | OnusOfflineSalesTransServiceTest | 139 | To Generate |
| 297 | OnusOriginTransMatchProcessServiceImpl | OnusOriginTransMatchProcessServiceImplTest | 361 | To Generate |
| 298 | OnusPreAuthDataUpdateServiceImpl | OnusPreAuthDataUpdateServiceImplTest | 333 | To Generate |
| 299 | OnusRefundTrans | OnusRefundTransTest | 307 | To Generate |
| 300 | OnusResponse8583HandlerServiceImpl | OnusResponse8583HandlerServiceImplTest | 302 | To Generate |
| 301 | OnusReversalTransService | OnusReversalTransServiceTest | 631 | To Generate |
| 302 | OnusTransactionClassifyServiceImpl | OnusTransactionClassifyServiceImplTest | 40 | To Generate |
| 303 | OnusVoidSaleTransService | OnusVoidSaleTransServiceTest | 487 | To Generate |
| 304 | OriginalTxnLogMatchServiceImpl | OriginalTxnLogMatchServiceImplTest | 87 | To Generate |
| 305 | OriginTransMatchProcessServiceImpl | OriginTransMatchProcessServiceImplTest | 507 | To Generate |
| 306 | OutStandingTransactionController | OutStandingTransactionControllerTest | 50 | To Generate |
| 307 | OutstandingTransServiceImpl | OutstandingTransServiceImplTest | 560 | To Generate |
| 308 | OutstandingTranTypeFactory | OutstandingTranTypeFactoryTest | 40 | To Generate |
| 309 | PartnerMarginAuthServiceImpl | PartnerMarginAuthServiceImplTest | 185 | To Generate |
| 310 | PartnerMarginItem | PartnerMarginItemTest | 155 | To Generate |
| 311 | PassWordCheckItem | PassWordCheckItemTest | 217 | To Generate |
| 312 | PosInstallmentItem | PosInstallmentItemTest | 98 | To Generate |
| 313 | PostAccountServiceImpl | PostAccountServiceImplTest | 493 | To Generate |
| 314 | PreAuthDataUpdateServiceImpl | PreAuthDataUpdateServiceImplTest | 304 | To Generate |
| 315 | PreAuthorizationLogController | PreAuthorizationLogControllerTest | 53 | To Generate |
| 316 | PreAuthorizationLogMapper | PreAuthorizationLogMapperTest | 101 | To Generate |
| 317 | PreAuthorizationLogSelfMapper | PreAuthorizationLogSelfMapperTest | 347 | To Generate |
| 318 | PreAuthorizationLogSelfSqlProvider | PreAuthorizationLogSelfSqlProviderTest | 49 | To Generate |
| 319 | PreAuthorizationLogServiceImpl | PreAuthorizationLogServiceImplTest | 391 | To Generate |
| 320 | PreAuthorizationLogSqlProvider | PreAuthorizationLogSqlProviderTest | 296 | To Generate |
| 321 | PrimarySecondaryCard | PrimarySecondaryCardTest | 63 | To Generate |
| 322 | RefundsTransServiceImpl | RefundsTransServiceImplTest | 103 | To Generate |
| 323 | RepayAuthorizationItem | RepayAuthorizationItemTest | 55 | To Generate |
| 324 | Request8583HandlerServiceImpl | Request8583HandlerServiceImplTest | 78 | To Generate |
| 325 | Response8583HandlerServiceImpl | Response8583HandlerServiceImplTest | 467 | To Generate |
| 326 | ReversalTransService | ReversalTransServiceTest | 89 | To Generate |
| 327 | ReversalTransServiceImpl | ReversalTransServiceImplTest | 120 | To Generate |
| 328 | RevocationReversalTransServiceImpl | RevocationReversalTransServiceImplTest | 68 | To Generate |
| 329 | RevocationTransServiceImpl | RevocationTransServiceImplTest | 66 | To Generate |
| 330 | RuleServiceImpl | RuleServiceImplTest | 92 | To Generate |
| 331 | RuleTransferImpl | RuleTransferImplTest | 434 | To Generate |
| 332 | ServerTypeAdaptive | ServerTypeAdaptiveTest | 51 | To Generate |
| 333 | SingleLimitItem | SingleLimitItemTest | 94 | To Generate |
| 334 | StandardOriginTransMatchProcessServiceImpl | StandardOriginTransMatchProcessServiceImplTest | 87 | To Generate |
| 335 | StandardUpdDataServiceImpl | StandardUpdDataServiceImplTest | 34 | To Generate |
| 336 | TradeModeStrategy | TradeModeStrategyTest | 26 | To Generate |
| 337 | TradeModeStrategyContext | TradeModeStrategyContextTest | 48 | To Generate |
| 338 | TransactionClassifyServiceImpl | TransactionClassifyServiceImplTest | 136 | To Generate |
| 339 | TransactionFeeController | TransactionFeeControllerTest | 61 | To Generate |
| 340 | TransPartitionKeyHelper | TransPartitionKeyHelperTest | 49 | To Generate |
| 341 | TransVelocityLogMapper | TransVelocityLogMapperTest | 90 | To Generate |
| 342 | TransVelocityLogSelfMapper | TransVelocityLogSelfMapperTest | 85 | To Generate |
| 343 | TransVelocityLogServiceImpl | TransVelocityLogServiceImplTest | 98 | To Generate |
| 344 | TransVelocityLogSqlProvider | TransVelocityLogSqlProviderTest | 125 | To Generate |
| 345 | TransVelocityStatisticsController | TransVelocityStatisticsControllerTest | 77 | To Generate |
| 346 | TransVelocityStatisticsJdbcService | TransVelocityStatisticsJdbcServiceTest | 62 | To Generate |
| 347 | TransVelocityStatisticsMapper | TransVelocityStatisticsMapperTest | 102 | To Generate |
| 348 | TransVelocityStatisticsSelfMapper | TransVelocityStatisticsSelfMapperTest | 142 | To Generate |
| 349 | TransVelocityStatisticsServiceImpl | TransVelocityStatisticsServiceImplTest | 212 | To Generate |
| 350 | TransVelocityStatisticsSqlProvider | TransVelocityStatisticsSqlProviderTest | 147 | To Generate |
| 351 | UcafCheckItem | UcafCheckItemTest | 624 | To Generate |
| 352 | UCardSpecialItem | UCardSpecialItemTest | 47 | To Generate |
| 353 | UpdateAuthDetailDataServiceImpl | UpdateAuthDetailDataServiceImplTest | 1908 | To Generate |
| 354 | UpiAccountFundingTransService | UpiAccountFundingTransServiceTest | 39 | To Generate |
| 355 | UpiAccountQueryService | UpiAccountQueryServiceTest | 135 | To Generate |
| 356 | UpiAccountVerificationTransService | UpiAccountVerificationTransServiceTest | 161 | To Generate |
| 357 | UpiAuthCheckDataPrepareServiceImpl | UpiAuthCheckDataPrepareServiceImplTest | 689 | To Generate |
| 358 | UpiAuthCheckProcessServiceImpl | UpiAuthCheckProcessServiceImplTest | 137 | To Generate |
| 359 | UpiAuthCheckWayDetailServiceImpl | UpiAuthCheckWayDetailServiceImplTest | 131 | To Generate |
| 360 | UpiAuthDataUpdateManager | UpiAuthDataUpdateManagerTest | 417 | To Generate |
| 361 | UpiAuthDataUpdateServiceImpl | UpiAuthDataUpdateServiceImplTest | 127 | To Generate |
| 362 | UpiAuthDetailDataModifyServiceImpl | UpiAuthDetailDataModifyServiceImplTest | 3442 | To Generate |
| 363 | UpiAuthProcessServiceImpl | UpiAuthProcessServiceImplTest | 619 | To Generate |
| 364 | UpiAuthTransPreprocessServiceImpl | UpiAuthTransPreprocessServiceImplTest | 740 | To Generate |
| 365 | UpiBalanceInquiryTransService | UpiBalanceInquiryTransServiceTest | 86 | To Generate |
| 366 | UpiCancellationTransService | UpiCancellationTransServiceTest | 411 | To Generate |
| 367 | UpiCancelReversalTransactionServiceImpl | UpiCancelReversalTransactionServiceImplTest | 637 | To Generate |
| 368 | UpiCashWithdrawalTransService | UpiCashWithdrawalTransServiceTest | 152 | To Generate |
| 369 | UpiCheckAuthResponseCodeServiceImpl | UpiCheckAuthResponseCodeServiceImplTest | 758 | To Generate |
| 370 | UpiCommissionRelationshipTransService | UpiCommissionRelationshipTransServiceTest | 156 | To Generate |
| 371 | UpiCreditConfirmService | UpiCreditConfirmServiceTest | 96 | To Generate |
| 372 | UpiDefaultMessageTypeService | UpiDefaultMessageTypeServiceTest | 138 | To Generate |
| 373 | UpiForwardTransactionServiceImpl | UpiForwardTransactionServiceImplTest | 83 | To Generate |
| 374 | UpiHandlerAuthServiceImpl | UpiHandlerAuthServiceImplTest | 47 | To Generate |
| 375 | UpiManualAuthService | UpiManualAuthServiceTest | 74 | To Generate |
| 376 | UpiOriginTransMatchProcessServiceImpl | UpiOriginTransMatchProcessServiceImplTest | 492 | To Generate |
| 377 | UpiPreAuthDataUpdateServiceImpl | UpiPreAuthDataUpdateServiceImplTest | 306 | To Generate |
| 378 | UpiPreAuthorizationCompletionTransService | UpiPreAuthorizationCompletionTransServiceTest | 207 | To Generate |
| 379 | UpiPreAuthorizationTransService | UpiPreAuthorizationTransServiceTest | 157 | To Generate |
| 380 | UpiPrimaryCreditTransService | UpiPrimaryCreditTransServiceTest | 147 | To Generate |
| 381 | UpiPurchaseTransService | UpiPurchaseTransServiceTest | 176 | To Generate |
| 382 | UpiRefundTransService | UpiRefundTransServiceTest | 251 | To Generate |
| 383 | UpiRemittanceTransService | UpiRemittanceTransServiceTest | 256 | To Generate |
| 384 | UpiResponse8583HandlerServiceImpl | UpiResponse8583HandlerServiceImplTest | 429 | To Generate |
| 385 | UpiReversalTransService | UpiReversalTransServiceTest | 550 | To Generate |
| 386 | UpiSettlementAdviceTransService | UpiSettlementAdviceTransServiceTest | 174 | To Generate |
| 387 | UpiTradeModeStrategyImpl | UpiTradeModeStrategyImplTest | 54 | To Generate |
| 388 | UpiTransactionClassifyServiceImpl | UpiTransactionClassifyServiceImplTest | 88 | To Generate |
| 389 | VelocityItem | VelocityItemTest | 830 | To Generate |
| 390 | VicomProperties | VicomPropertiesTest | 21 | To Generate |
| 391 | VisaAccountVerificationTransAfterServiceImpl | VisaAccountVerificationTransAfterServiceImplTest | 37 | To Generate |
| 392 | VisaAccountVerificationTransService | VisaAccountVerificationTransServiceTest | 88 | To Generate |
| 393 | VisaAFDCompAdviceAfterProcessServiceImpl | VisaAFDCompAdviceAfterProcessServiceImplTest | 73 | To Generate |
| 394 | VisaAFDCompAdviceProcessUpdDataServiceImpl | VisaAFDCompAdviceProcessUpdDataServiceImplTest | 58 | To Generate |
| 395 | VisaAuthAdviceTransHandler | VisaAuthAdviceTransHandlerTest | 173 | To Generate |
| 396 | VisaAuthAdviceTransService | VisaAuthAdviceTransServiceTest | 249 | To Generate |
| 397 | VisaAuthCheckDataPrepareServiceImpl | VisaAuthCheckDataPrepareServiceImplTest | 534 | To Generate |
| 398 | VisaAuthDataUpdateManager | VisaAuthDataUpdateManagerTest | 199 | To Generate |
| 399 | VisaAuthDetailDataModifyServiceImpl | VisaAuthDetailDataModifyServiceImplTest | 1512 | To Generate |
| 400 | VisaAuthProcessServiceImpl | VisaAuthProcessServiceImplTest | 1199 | To Generate |
| 401 | VisaAuthRequestTransService | VisaAuthRequestTransServiceTest | 162 | To Generate |
| 402 | VisaAuthTransPreprocessServiceImpl | VisaAuthTransPreprocessServiceImplTest | 834 | To Generate |
| 403 | VisaBalanceInquiryTransService | VisaBalanceInquiryTransServiceTest | 118 | To Generate |
| 404 | VisaCancellationTransService | VisaCancellationTransServiceTest | 218 | To Generate |
| 405 | VisaCancelReversalTransactionServiceImpl | VisaCancelReversalTransactionServiceImplTest | 80 | To Generate |
| 406 | VisaCashWithdrawalTransService | VisaCashWithdrawalTransServiceTest | 153 | To Generate |
| 407 | VisaCheckAuthResponseCodeServiceImpl | VisaCheckAuthResponseCodeServiceImplTest | 819 | To Generate |
| 408 | VisaCheckItemProcessServiceImpl | VisaCheckItemProcessServiceImplTest | 50 | To Generate |
| 409 | VisaDefaultAuthAfterProcessServiceImpl | VisaDefaultAuthAfterProcessServiceImplTest | 12 | To Generate |
| 410 | VisaDefaultAuthProcessUpdDataServiceImpl | VisaDefaultAuthProcessUpdDataServiceImplTest | 34 | To Generate |
| 411 | VisaFileUpdateAdviceTransService | VisaFileUpdateAdviceTransServiceTest | 67 | To Generate |
| 412 | VisaFileUpdateHandler | VisaFileUpdateHandlerTest | 242 | To Generate |
| 413 | VisaHandlerAuthServiceImpl | VisaHandlerAuthServiceImplTest | 76 | To Generate |
| 414 | VisaIncrementalAuthorizationTransService | VisaIncrementalAuthorizationTransServiceTest | 118 | To Generate |
| 415 | VisaIncrementalRevProcessUpdDataServiceImpl | VisaIncrementalRevProcessUpdDataServiceImplTest | 261 | To Generate |
| 416 | VisaInstallmentTransService | VisaInstallmentTransServiceTest | 30 | To Generate |
| 417 | VisaLogAuthProcessUpdDataServiceImpl | VisaLogAuthProcessUpdDataServiceImplTest | 24 | To Generate |
| 418 | VisaManualAuthService | VisaManualAuthServiceTest | 79 | To Generate |
| 419 | VisaOriginTransMatchProcessServiceImpl | VisaOriginTransMatchProcessServiceImplTest | 341 | To Generate |
| 420 | VisaPartialAuthorizationTransService | VisaPartialAuthorizationTransServiceTest | 154 | To Generate |
| 421 | VisaPaymentAuthAfterProcessServiceImpl | VisaPaymentAuthAfterProcessServiceImplTest | 27 | To Generate |
| 422 | VisaPaymentTransService | VisaPaymentTransServiceTest | 122 | To Generate |
| 423 | VisaPreAuthCompAfterProcessServiceImpl | VisaPreAuthCompAfterProcessServiceImplTest | 113 | To Generate |
| 424 | VisaPreAuthCompProcessUpdDataServiceImpl | VisaPreAuthCompProcessUpdDataServiceImplTest | 52 | To Generate |
| 425 | VisaPreAuthCompRevProcessUpdDataServiceImpl | VisaPreAuthCompRevProcessUpdDataServiceImplTest | 74 | To Generate |
| 426 | VisaPreAuthProcessRevUpdDataServiceImpl | VisaPreAuthProcessRevUpdDataServiceImplTest | 103 | To Generate |
| 427 | VisaRefundTransService | VisaRefundTransServiceTest | 274 | To Generate |
| 428 | VisaRequest8583HandlerServiceImpl | VisaRequest8583HandlerServiceImplTest | 56 | To Generate |
| 429 | VisaResponse8583HandlerServiceImpl | VisaResponse8583HandlerServiceImplTest | 219 | To Generate |
| 430 | VisaRetailTransService | VisaRetailTransServiceTest | 128 | To Generate |
| 431 | VisaReversalAdviceTransService | VisaReversalAdviceTransServiceTest | 163 | To Generate |
| 432 | VisaReversalTransService | VisaReversalTransServiceTest | 269 | To Generate |
| 433 | VisaStandardAuthProcessUpdDataServiceImpl | VisaStandardAuthProcessUpdDataServiceImplTest | 31 | To Generate |
| 434 | VisaTradeModeStrategyImpl | VisaTradeModeStrategyImplTest | 39 | To Generate |
| 435 | VisaTransactionClassifyServiceImpl | VisaTransactionClassifyServiceImplTest | 226 | To Generate |
---

## Statistics

### Distribution by Lines of Code

- Extra Large Classes (>1000 lines): 14
- Large Classes (500-1000 lines): 44
- Medium Classes (100-499 lines): 205
- Small Classes (<100 lines): 172

### Top 10 Largest Classes

- UpiAuthDetailDataModifyServiceImpl: 3442 lines
- AuthDetailDataModifyServiceImpl: 3326 lines
- MastercAuthDetailDataModifyServiceImpl: 3135 lines
- OnusAuthDetailDataModifyServiceImpl: 2084 lines
- UpdateAuthDetailDataServiceImpl: 1908 lines
- VisaAuthDetailDataModifyServiceImpl: 1512 lines
- AuthorizationLogSqlProvider: 1257 lines
- VisaAuthProcessServiceImpl: 1199 lines
- FieldInAndOutProcessServiceImpl: 1126 lines
- MastercAuthTransPreprocessServiceImpl: 1123 lines
### Top 10 Smallest Classes

- AuthProcessUpdDataService: 9 lines
- VisaDefaultAuthAfterProcessServiceImpl: 12 lines
- ConsumerExe: 12 lines
- DefaultAuthAfterProcessServiceImpl: 12 lines
- McDefaultAuthAfterProcessServiceImpl: 13 lines
- AuthPrePostInfoModifyService: 14 lines
- AuthProcessService: 16 lines
- ConfigBean: 19 lines
- ITransRouting: 19 lines
- AuthAssert: 20 lines
---

## Data Generation Instructions

This document is generated by the following PowerShell script to ensure 100% accuracy:

`powershell
Get-ChildItem -Path "anytxn-authorization-sdk\src\main\java" -Recurse -Filter "*.java" |
    Where-Object { $_.Name -notmatch "Test" } |
    ForEach-Object {
        $content = Get-Content $_.FullName -Raw
        $lines = if ($content) { $content.Split("`n").Count } else { 0 }
        [PSCustomObject]@{
            ClassName = $_.BaseName
            Lines = $lines
        }
    } | Sort-Object ClassName
`

**Generation Time**: 2025-07-31 18:22:29
