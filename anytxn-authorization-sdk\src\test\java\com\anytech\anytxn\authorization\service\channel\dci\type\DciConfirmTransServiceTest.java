package com.anytech.anytxn.authorization.service.channel.dci.type;

import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.service.auth.IOriginalTxnLogMatchService;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.auth.AuthCheckDataPrepareServiceImpl;
import com.anytech.anytxn.authorization.service.auth.AuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.transaction.PostAccountServiceImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthorizationLogDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DciConfirmTransService单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DCI确认交易服务测试")
class DciConfirmTransServiceTest {

    @Mock
    private IOriginalTxnLogMatchService originalTxnLogMatchService;

    @Mock
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Mock
    private AuthCheckDataPrepareServiceImpl authCheckDataPrepareService;

    @Mock
    private PostAccountServiceImpl postAccountService;

    @InjectMocks
    private DciConfirmTransService dciConfirmTransService;

    private AuthRecordedDTO authRecordedDTO;
    private ISO8583DTO iso8583DTO;
    private AuthorizationLogDTO authorizationLogDTO;
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        authRecordedDTO = new AuthRecordedDTO();
        iso8583DTO = new ISO8583DTO();
        authorizationLogDTO = new AuthorizationLogDTO();
        authorizationCheckProcessingPayload = mock(AuthorizationCheckProcessingPayload.class);

        // 设置基础数据
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        authRecordedDTO.setAuthGlobalFlowNumber("*********");
        authRecordedDTO.setAuthCardNumber("****************");

        authorizationLogDTO.setAuthLogId("LOG123456");
        authorizationLogDTO.setResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        authorizationLogDTO.setAuthCode("AUTH123");
    }

    @Test
    @DisplayName("请求体检查 - 空实现")
    void testReqBodyCheck() {
        // Given & When & Then
        assertDoesNotThrow(() -> {
            dciConfirmTransService.reqBodyCheck(authRecordedDTO, iso8583DTO);
        });
    }

    @Test
    @DisplayName("交易处理 - 授权失败场景")
    void testTransHandler_AuthFailure() throws Exception {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.SYSTEM_MALFUNCTION.getCode());

        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(true);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            // When
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(originalTxnLogMatchService, never()).debitOriginWithAuthLog(any());
        }
    }

    @Test
    @DisplayName("交易处理 - 匹配到原交易且原交易成功")
    void testTransHandler_MatchOriginalTransSuccess() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authorizationLogDTO.getResponseCode())).thenReturn(true);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(authorizationLogDTO);

            // When
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            assertEquals(authorizationLogDTO.getResponseCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
            assertEquals(authorizationLogDTO.getAuthCode(), authRecordedDTO.getAuthAuthCode());

            verify(originalTxnLogMatchService).debitOriginWithAuthLog(authRecordedDTO);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(authCheckDataPrepareService, never()).prepareAuthData(any());
        }
    }

    @Test
    @DisplayName("交易处理 - 匹配到原交易但原交易失败")
    void testTransHandler_MatchOriginalTransFailure() throws Exception {
        // Given
        authorizationLogDTO.setResponseCode(AuthResponseCodeEnum.SYSTEM_MALFUNCTION.getCode());
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authorizationLogDTO.getResponseCode())).thenReturn(false);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(authorizationLogDTO);
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            
            // When
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.CREDIT_CONFIRM_ORIGINAL_TRANS_ERROR.getErrorDetailCode(), authRecordedDTO.getErrorDetailCode());
            assertEquals(AuthTransTypeEnum.NORMAL_TRANS.getCode(), authRecordedDTO.getAuthTransactionTypeCode());
            assertNotNull(authRecordedDTO.getAuthAuthCode());
            assertEquals(6, authRecordedDTO.getAuthAuthCode().length());
            
            verify(originalTxnLogMatchService).debitOriginWithAuthLog(authRecordedDTO);
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authDetailDataModifyService).outStandingTransModify(authorizationCheckProcessingPayload);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 未匹配到原交易")
    void testTransHandler_NoMatchOriginalTrans() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(null);
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            
            // When
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.CREDIT_CONFIRM_NOT_MATCH.getErrorDetailCode(), authRecordedDTO.getErrorDetailCode());
            assertEquals(AuthTransTypeEnum.NORMAL_TRANS.getCode(), authRecordedDTO.getAuthTransactionTypeCode());
            assertNotNull(authRecordedDTO.getAuthAuthCode());
            assertEquals(6, authRecordedDTO.getAuthAuthCode().length());
            
            verify(originalTxnLogMatchService).debitOriginWithAuthLog(authRecordedDTO);
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authDetailDataModifyService).outStandingTransModify(authorizationCheckProcessingPayload);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 数据准备异常")
    void testTransHandler_DataPrepareException() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(null);
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenThrow(new RuntimeException("Data prepare failed"));
            
            // When & Then
            assertThrows(RuntimeException.class, () -> {
                dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            });
            
            verify(originalTxnLogMatchService).debitOriginWithAuthLog(authRecordedDTO);
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 原交易日志匹配异常")
    void testTransHandler_OriginalTxnMatchException() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenThrow(new RuntimeException("Original transaction match failed"));
            
            // When & Then
            assertThrows(RuntimeException.class, () -> {
                dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            });
            
            verify(originalTxnLogMatchService).debitOriginWithAuthLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 验证授权码生成")
    void testTransHandler_AuthCodeGeneration() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(null);
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            
            // When
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertNotNull(authRecordedDTO.getAuthAuthCode());
            assertEquals(6, authRecordedDTO.getAuthAuthCode().length());
            assertTrue(authRecordedDTO.getAuthAuthCode().matches("\\d{6}"));
        }
    }

    @Test
    @DisplayName("交易处理 - 验证日志记录")
    void testTransHandler_LoggingVerification() throws Exception {
        // Given
        authorizationLogDTO.setAuthLogId("ORIGINAL_LOG_123");
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authorizationLogDTO.getResponseCode())).thenReturn(true);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(authorizationLogDTO);
            
            // When
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            verify(originalTxnLogMatchService).debitOriginWithAuthLog(authRecordedDTO);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("更新数据 - 返回0")
    void testUpdateData() {
        // Given & When
        int result = dciConfirmTransService.updateData(authorizationCheckProcessingPayload);
        
        // Then
        assertEquals(0, result);
    }

    @Test
    @DisplayName("交易处理 - 边界条件测试")
    void testTransHandler_BoundaryConditions() throws Exception {
        // Given - 空的原交易日志ID
        authorizationLogDTO.setAuthLogId("");
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authorizationLogDTO.getResponseCode())).thenReturn(true);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(authorizationLogDTO);
            
            // When
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(authorizationLogDTO.getResponseCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
            
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 多次调用幂等性")
    void testTransHandler_Idempotency() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {

            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authorizationLogDTO.getResponseCode())).thenReturn(true);
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(originalTxnLogMatchService.debitOriginWithAuthLog(authRecordedDTO))
                    .thenReturn(authorizationLogDTO);
            
            // When - 多次调用
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            String firstAuthCode = authRecordedDTO.getAuthAuthCode();
            
            dciConfirmTransService.transHandler(authRecordedDTO, iso8583DTO);
            String secondAuthCode = authRecordedDTO.getAuthAuthCode();
            
            // Then - 验证结果一致性
            assertEquals(firstAuthCode, secondAuthCode);
            assertEquals(authorizationLogDTO.getResponseCode(), authRecordedDTO.getAuthResponseCode());
            
            verify(originalTxnLogMatchService, times(2)).debitOriginWithAuthLog(authRecordedDTO);
            verify(authDetailDataModifyService, times(2)).addAuthorizationLog(authRecordedDTO);
        }
    }
}
