package com.anytech.anytxn.authorization.service.fegin.cardholder;

import com.anytech.anytxn.authorization.client.cardholder.AuthCardHolderFeign;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerBasicInfoDTO;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * @description CardHolderFeignFallBack的单元测试类
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("持卡人Feign回退处理测试")
class CardHolderFeignFallBackTest {

    private CardHolderFeignFallBack cardHolderFeignFallBack;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("001");
        lenient().when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        cardHolderFeignFallBack = new CardHolderFeignFallBack();
    }

    @Test
    @DisplayName("测试create方法-创建回退实例")
    void testCreate_Success() {
        // 准备数据
        RuntimeException testException = new RuntimeException("服务调用失败");

        // 执行测试
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);

        // 验证结果
        assertNotNull(fallbackFeign);
        assertTrue(fallbackFeign instanceof AuthCardHolderFeign);
    }

    @Test
    @DisplayName("测试create方法-空异常参数")
    void testCreate_NullException() {
        // 执行测试
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(null);

        // 验证结果
        assertNotNull(fallbackFeign);
        assertTrue(fallbackFeign instanceof AuthCardHolderFeign);
    }

    @Test
    @DisplayName("测试回退实例-getCustomerAuthInfoOrgAndCid方法")
    void testFallbackFeign_GetCustomerAuthInfoOrgAndCid() {
        // 准备数据
        RuntimeException testException = new RuntimeException("服务调用失败");
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);

        String organizationNumber = "001";
        String customerId = "123456";
        String cid = "CID123";

        // 执行测试
        AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> response = 
            fallbackFeign.getCustomerAuthInfoOrgAndCid(organizationNumber, customerId, cid);

        // 验证结果
        assertNotNull(response);
        assertEquals(AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), response.getCode());
        assertEquals("CardHolder服务不可用，无法获取客户授权信息", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试回退实例-getCustomerBasicInfoByOrgAndCid方法")
    void testFallbackFeign_GetCustomerBasicInfoByOrgAndCid() {
        // 准备数据
        RuntimeException testException = new RuntimeException("服务调用失败");
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);

        String organizationNumber = "001";
        String customerId = "123456";
        String cid = "CID123";

        // 执行测试
        AnyTxnHttpResponse<CustomerBasicInfoDTO> response = 
            fallbackFeign.getCustomerBasicInfoByOrgAndCid(organizationNumber, customerId, cid);

        // 验证结果
        assertNotNull(response);
        assertEquals(AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), response.getCode());
        assertEquals("CardHolder服务不可用，无法获取客户基本信息", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    @DisplayName("测试回退实例-空参数场景")
    void testFallbackFeign_NullParameters() {
        // 准备数据
        RuntimeException testException = new RuntimeException("服务调用失败");
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);

        // 执行测试 - getCustomerAuthInfoOrgAndCid
        AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> authResponse = 
            fallbackFeign.getCustomerAuthInfoOrgAndCid(null, null, null);

        // 验证结果
        assertNotNull(authResponse);
        assertEquals(AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), authResponse.getCode());

        // 执行测试 - getCustomerBasicInfoByOrgAndCid
        AnyTxnHttpResponse<CustomerBasicInfoDTO> basicResponse =
            fallbackFeign.getCustomerBasicInfoByOrgAndCid(null, null, null);

        // 验证结果
        assertNotNull(basicResponse);
        assertEquals(AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), basicResponse.getCode());
    }

    @Test
    @DisplayName("测试回退实例-多次调用")
    void testFallbackFeign_MultipleCalls() {
        // 准备数据
        RuntimeException testException = new RuntimeException("服务调用失败");
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);

        String organizationNumber = "001";
        String customerId = "123456";
        String cid = "CID123";

        // 执行多次调用
        AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> response1 = 
            fallbackFeign.getCustomerAuthInfoOrgAndCid(organizationNumber, customerId, cid);
        AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> response2 = 
            fallbackFeign.getCustomerAuthInfoOrgAndCid(organizationNumber, customerId, cid);

        // 验证结果
        assertNotNull(response1);
        assertNotNull(response2);
        assertEquals(response1.getCode(), response2.getCode());
        assertEquals(response1.getMessage(), response2.getMessage());
    }

    @Test
    @DisplayName("测试不同异常类型")
    void testCreate_DifferentExceptionTypes() {
        // 测试不同类型的异常
        Exception[] exceptions = {
            new RuntimeException("运行时异常"),
            new IllegalArgumentException("参数异常"),
            new NullPointerException("空指针异常"),
            new Exception("通用异常")
        };

        for (Exception exception : exceptions) {
            // 执行测试
            AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(exception);

            // 验证结果
            assertNotNull(fallbackFeign);
            
            // 测试回退方法
            AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> response = 
                fallbackFeign.getCustomerAuthInfoOrgAndCid("001", "123", "CID123");
            
            assertNotNull(response);
            assertEquals(AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), response.getCode());
        }
    }

    @Test
    @DisplayName("测试FallbackFactory接口实现")
    void testFallbackFactoryInterface() {
        // 验证实现了FallbackFactory接口
        assertTrue(cardHolderFeignFallBack instanceof feign.hystrix.FallbackFactory);
    }

    @Test
    @DisplayName("测试Service注解")
    void testServiceAnnotation() {
        // 验证类上有@Service注解
        assertTrue(cardHolderFeignFallBack.getClass().isAnnotationPresent(org.springframework.stereotype.Service.class));
    }

    @Test
    @DisplayName("测试回退实例-验证返回值类型")
    void testFallbackFeign_ReturnTypes() {
        // 准备数据
        RuntimeException testException = new RuntimeException("服务调用失败");
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);

        // 执行测试
        AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> authResponse = 
            fallbackFeign.getCustomerAuthInfoOrgAndCid("001", "123", "CID123");
        AnyTxnHttpResponse<CustomerBasicInfoDTO> basicResponse = 
            fallbackFeign.getCustomerBasicInfoByOrgAndCid("001", "123", "CID123");

        // 验证返回值类型
        assertTrue(authResponse instanceof AnyTxnHttpResponse);
        assertTrue(basicResponse instanceof AnyTxnHttpResponse);
        
        // 验证泛型类型（通过运行时检查）
        assertNotNull(authResponse.getCode());
        assertNotNull(authResponse.getMessage());
        assertNotNull(basicResponse.getCode());
        assertNotNull(basicResponse.getMessage());
    }

    @Test
    @DisplayName("测试异常消息记录")
    void testExceptionMessageLogging() {
        // 准备数据
        String errorMessage = "特定的错误消息";
        RuntimeException testException = new RuntimeException(errorMessage);

        // 执行测试 - create方法会记录异常消息
        AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);

        // 验证回退实例创建成功
        assertNotNull(fallbackFeign);
        
        // 验证回退方法正常工作
        AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> response = 
            fallbackFeign.getCustomerAuthInfoOrgAndCid("001", "123", "CID123");
        
        assertNotNull(response);
        assertEquals(AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), response.getCode());
    }

    @Test
    @DisplayName("测试线程安全性")
    void testThreadSafety() {
        // 验证多线程环境下的安全性
        RuntimeException testException = new RuntimeException("并发测试异常");
        
        Runnable task = () -> {
            for (int i = 0; i < 10; i++) {
                AuthCardHolderFeign fallbackFeign = cardHolderFeignFallBack.create(testException);
                assertNotNull(fallbackFeign);
                
                AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> response = 
                    fallbackFeign.getCustomerAuthInfoOrgAndCid("001", "123", "CID123");
                assertNotNull(response);
                assertEquals(AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), response.getCode());
            }
        };

        // 创建多个线程并发执行
        Thread thread1 = new Thread(task);
        Thread thread2 = new Thread(task);
        Thread thread3 = new Thread(task);

        assertDoesNotThrow(() -> {
            thread1.start();
            thread2.start();
            thread3.start();

            thread1.join();
            thread2.join();
            thread3.join();
        });
    }
}
