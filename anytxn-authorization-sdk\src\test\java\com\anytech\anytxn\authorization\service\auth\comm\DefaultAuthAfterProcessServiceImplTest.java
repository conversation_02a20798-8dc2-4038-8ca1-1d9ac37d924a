package com.anytech.anytxn.authorization.service.auth.comm;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.auth.AuthProcessUpdDataService;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthItemCheckResCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthTrancactionStatusEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * @description DefaultAuthAfterProcessServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("默认授权后处理服务实现测试")
class DefaultAuthAfterProcessServiceImplTest {

    @Mock
    private UpdateAuthDetailDataServiceImpl updateAuthDetailDataServiceImpl;

    @Mock
    private AuthProcessUpdDataService authProcessUpdDataService;

    @InjectMocks
    private DefaultAuthAfterProcessServiceImpl defaultAuthAfterProcessServiceImpl;

    private AuthorizationCheckProcessingPayload payload;
    private AuthRecordedDTO authRecordedDTO;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        when(OrgNumberUtils.getOrg()).thenReturn("001");
        when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 准备测试数据
        payload = new AuthorizationCheckProcessingPayload();
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("100.00"));
        payload.setAuthRecordedDTO(authRecordedDTO);
    }

    @Test
    @DisplayName("测试execute方法-成功执行")
    void testExecute_Success() {
        // 准备数据 - 设置成功的响应码
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(authProcessUpdDataService);
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

        // 执行测试
        assertDoesNotThrow(() -> {
            defaultAuthAfterProcessServiceImpl.execute(payload);
        });

        // 验证调用
        verify(authProcessUpdDataService).updateData(payload);
        // 验证状态被设置为成功
        assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
    }

    @Test
    @DisplayName("测试execute方法-失败响应码")
    void testExecute_FailureResponseCode() {
        // 设置失败的响应码
        authRecordedDTO.setAuthResponseCode("05"); // 拒绝码
        defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(authProcessUpdDataService);

        // 执行测试 - 应该不抛出异常
        assertDoesNotThrow(() -> {
            defaultAuthAfterProcessServiceImpl.execute(payload);
        });

        // 验证没有调用updateData，因为响应码不是成功的
        verify(authProcessUpdDataService, never()).updateData(any());
    }

    @Test
    @DisplayName("测试execute方法-空payload参数")
    void testExecute_NullPayload() {
        // 准备数据
        defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(authProcessUpdDataService);

        // 执行测试 - 传入null payload会抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            defaultAuthAfterProcessServiceImpl.execute(null);
        });

        // 验证没有调用updateData
        verify(authProcessUpdDataService, never()).updateData(any());
    }

    @Test
    @DisplayName("测试execute方法-AuthProcessUpdDataService抛出异常")
    void testExecute_AuthProcessUpdDataServiceThrowsException() {
        // 准备数据 - 设置成功的响应码
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(authProcessUpdDataService);
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenThrow(new RuntimeException("更新数据异常"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            defaultAuthAfterProcessServiceImpl.execute(payload);
        });

        // 验证调用
        verify(authProcessUpdDataService).updateData(payload);
    }

    @Test
    @DisplayName("测试setAuthProcessUpdDataService方法")
    void testSetAuthProcessUpdDataService() {
        // 准备数据
        AuthProcessUpdDataService newService = mock(AuthProcessUpdDataService.class);
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());

        // 执行测试
        assertDoesNotThrow(() -> {
            defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(newService);
        });

        // 验证设置成功 - 通过执行execute方法来验证
        when(newService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

        defaultAuthAfterProcessServiceImpl.execute(payload);
        verify(newService).updateData(payload);
    }

    @Test
    @DisplayName("测试setAuthProcessUpdDataService方法-设置null")
    void testSetAuthProcessUpdDataService_Null() {
        // 执行测试
        assertDoesNotThrow(() -> {
            defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(null);
        });

        // 验证设置null后执行不会出错
        assertDoesNotThrow(() -> {
            defaultAuthAfterProcessServiceImpl.execute(payload);
        });
    }

    @Test
    @DisplayName("测试继承关系-验证是AbstractAuthAfterProcessService的子类")
    void testInheritance() {
        // 验证继承关系
        assertTrue(defaultAuthAfterProcessServiceImpl instanceof AbstractAuthAfterProcessService);
    }

    @Test
    @DisplayName("测试execute方法-多次调用")
    void testExecute_MultipleCalls() {
        // 准备数据
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(authProcessUpdDataService);
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

        // 执行多次调用
        defaultAuthAfterProcessServiceImpl.execute(payload);
        defaultAuthAfterProcessServiceImpl.execute(payload);
        defaultAuthAfterProcessServiceImpl.execute(payload);

        // 验证调用次数
        verify(authProcessUpdDataService, times(3)).updateData(payload);
    }

    @Test
    @DisplayName("测试execute方法-不同payload参数")
    void testExecute_DifferentPayloads() {
        // 准备数据
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(authProcessUpdDataService);
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

        AuthorizationCheckProcessingPayload anotherPayload = new AuthorizationCheckProcessingPayload();
        AuthRecordedDTO anotherAuthDTO = new AuthRecordedDTO();
        anotherAuthDTO.setAuthCardNumber("9876543210987654");
        anotherAuthDTO.setAuthTransactionAmount(new BigDecimal("200.00"));
        anotherAuthDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        anotherPayload.setAuthRecordedDTO(anotherAuthDTO);

        // 执行测试
        defaultAuthAfterProcessServiceImpl.execute(payload);
        defaultAuthAfterProcessServiceImpl.execute(anotherPayload);

        // 验证调用
        verify(authProcessUpdDataService).updateData(payload);
        verify(authProcessUpdDataService).updateData(anotherPayload);
        verify(authProcessUpdDataService, times(2)).updateData(any(AuthorizationCheckProcessingPayload.class));
    }

    @Test
    @DisplayName("测试execute方法-验证返回值处理")
    void testExecute_ReturnValueHandling() {
        // 准备数据
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CHECK_RESPONSE.getCode());
        defaultAuthAfterProcessServiceImpl.setAuthProcessUpdDataService(authProcessUpdDataService);
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

        // 执行测试 - execute方法是void，不返回值
        assertDoesNotThrow(() -> {
            defaultAuthAfterProcessServiceImpl.execute(payload);
        });

        // 验证调用
        verify(authProcessUpdDataService).updateData(payload);
    }

    @Test
    @DisplayName("测试类注解和组件扫描")
    void testComponentAnnotation() {
        // 验证类上有@Component注解（通过Spring容器能够扫描到）
        assertTrue(defaultAuthAfterProcessServiceImpl.getClass().isAnnotationPresent(org.springframework.stereotype.Component.class));
    }
}
