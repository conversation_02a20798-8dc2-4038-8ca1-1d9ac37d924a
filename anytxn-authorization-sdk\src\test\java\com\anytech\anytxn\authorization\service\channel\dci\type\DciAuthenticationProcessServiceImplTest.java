package com.anytech.anytxn.authorization.service.channel.dci.type;

import com.anytech.anytxn.authorization.base.domain.dto.DCIDcs8583ReqDTO;
import com.anytech.anytxn.authorization.base.domain.dto.DCIDcs8583RspDTO;
import com.anytech.anytxn.authorization.client.encryption.AuthEncryptionFeignClient;
import com.anytech.anytxn.authorization.client.gateway.AnyTechFeignClient;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DciAuthenticationProcessServiceImpl单元测试类
 * 
 * <AUTHOR> Assistant
 * @datetime 2025/8/1
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DCI认证处理服务测试")
class DciAuthenticationProcessServiceImplTest {

    @Mock
    private AnyTechFeignClient anyTechFeignClient;
    
    @Mock
    private AuthEncryptionFeignClient authEncryptionFeignClient;

    @InjectMocks
    private DciAuthenticationProcessServiceImpl dciAuthenticationProcessService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 参数为空")
    void testToDciAuthAuthentication_NullParameter() throws Exception {
        // Arrange
        DCIDcs8583RspDTO mockRspDTO = new DCIDcs8583RspDTO();
        AnyTxnHttpResponse<DCIDcs8583RspDTO> mockResponse = AnyTxnHttpResponse.success(mockRspDTO);

        when(anyTechFeignClient.acqToDci(null)).thenReturn(mockResponse);
        
        // Act
        DCIDcs8583RspDTO result = dciAuthenticationProcessService.toDciAuthAuthentication(null);
        
        // Assert
        assertNotNull(result);
        verify(anyTechFeignClient, times(1)).acqToDci(null);
        verify(authEncryptionFeignClient, never()).zpkFromLmkToZmk();
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 功能码814密钥交换")
    void testToDciAuthAuthentication_FunctionCode814() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024("814");
        
        AnyTxnHttpResponse<String> keyResponse = AnyTxnHttpResponse.success("1234567890ABCDEF12345678"); // 24位密钥数据

        DCIDcs8583RspDTO mockRspDTO = new DCIDcs8583RspDTO();
        AnyTxnHttpResponse<DCIDcs8583RspDTO> mockResponse = AnyTxnHttpResponse.success(mockRspDTO);
        
        when(authEncryptionFeignClient.zpkFromLmkToZmk()).thenReturn(keyResponse);
        when(anyTechFeignClient.acqToDci(reqDTO)).thenReturn(mockResponse);
        
        // Act
        DCIDcs8583RspDTO result = dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);
        
        // Assert
        assertNotNull(result);
        assertEquals("1234567890ABCDEF", reqDTO.getF096()); // 去掉后8位
        verify(authEncryptionFeignClient, times(1)).zpkFromLmkToZmk();
        verify(anyTechFeignClient, times(1)).acqToDci(reqDTO);
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 功能码814密钥数据为空")
    void testToDciAuthAuthentication_FunctionCode814_EmptyKeyData() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024("814");
        
        AnyTxnHttpResponse<String> keyResponse = AnyTxnHttpResponse.success(""); // 空密钥数据

        DCIDcs8583RspDTO mockRspDTO = new DCIDcs8583RspDTO();
        AnyTxnHttpResponse<DCIDcs8583RspDTO> mockResponse = AnyTxnHttpResponse.success(mockRspDTO);
        
        when(authEncryptionFeignClient.zpkFromLmkToZmk()).thenReturn(keyResponse);
        when(anyTechFeignClient.acqToDci(reqDTO)).thenReturn(mockResponse);
        
        // Act
        DCIDcs8583RspDTO result = dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);
        
        // Assert
        assertNotNull(result);
        assertNull(reqDTO.getF096()); // 不应该设置F096字段
        verify(authEncryptionFeignClient, times(1)).zpkFromLmkToZmk();
        verify(anyTechFeignClient, times(1)).acqToDci(reqDTO);
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 功能码814密钥数据长度不足")
    void testToDciAuthAuthentication_FunctionCode814_ShortKeyData() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024("814");
        
        AnyTxnHttpResponse<String> keyResponse = AnyTxnHttpResponse.success("1234567"); // 长度不足8位

        DCIDcs8583RspDTO mockRspDTO = new DCIDcs8583RspDTO();
        AnyTxnHttpResponse<DCIDcs8583RspDTO> mockResponse = AnyTxnHttpResponse.success(mockRspDTO);
        
        when(authEncryptionFeignClient.zpkFromLmkToZmk()).thenReturn(keyResponse);
        when(anyTechFeignClient.acqToDci(reqDTO)).thenReturn(mockResponse);
        
        // Act
        DCIDcs8583RspDTO result = dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);
        
        // Assert
        assertNotNull(result);
        assertNull(reqDTO.getF096()); // 不应该设置F096字段
        verify(authEncryptionFeignClient, times(1)).zpkFromLmkToZmk();
        verify(anyTechFeignClient, times(1)).acqToDci(reqDTO);
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 功能码640")
    void testToDciAuthAuthentication_FunctionCode640() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024("640");
        
        DCIDcs8583RspDTO mockRspDTO = new DCIDcs8583RspDTO();
        AnyTxnHttpResponse<DCIDcs8583RspDTO> mockResponse = AnyTxnHttpResponse.success(mockRspDTO);
        
        when(anyTechFeignClient.acqToDci(reqDTO)).thenReturn(mockResponse);
        
        // Act
        DCIDcs8583RspDTO result = dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);
        
        // Assert
        assertNotNull(result);
        assertEquals("680", reqDTO.getF039()); // 应该设置F039为680
        verify(anyTechFeignClient, times(1)).acqToDci(reqDTO);
        verify(authEncryptionFeignClient, never()).zpkFromLmkToZmk();
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 其他功能码")
    void testToDciAuthAuthentication_OtherFunctionCode() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024("999"); // 其他功能码
        
        DCIDcs8583RspDTO mockRspDTO = new DCIDcs8583RspDTO();
        AnyTxnHttpResponse<DCIDcs8583RspDTO> mockResponse = AnyTxnHttpResponse.success(mockRspDTO);

        when(anyTechFeignClient.acqToDci(reqDTO)).thenReturn(mockResponse);

        // Act
        DCIDcs8583RspDTO result = dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);

        // Assert
        assertNotNull(result);
        assertNull(reqDTO.getF096()); // 不应该设置F096字段
        assertNull(reqDTO.getF039()); // 不应该设置F039字段
        verify(anyTechFeignClient, times(1)).acqToDci(reqDTO);
        verify(authEncryptionFeignClient, never()).zpkFromLmkToZmk();
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 功能码为空")
    void testToDciAuthAuthentication_NullFunctionCode() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024(null);

        DCIDcs8583RspDTO mockRspDTO = new DCIDcs8583RspDTO();
        AnyTxnHttpResponse<DCIDcs8583RspDTO> mockResponse = AnyTxnHttpResponse.success(mockRspDTO);
        
        when(anyTechFeignClient.acqToDci(reqDTO)).thenReturn(mockResponse);
        
        // Act
        DCIDcs8583RspDTO result = dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);
        
        // Assert
        assertNotNull(result);
        assertNull(reqDTO.getF096()); // 不应该设置F096字段
        assertNull(reqDTO.getF039()); // 不应该设置F039字段
        verify(anyTechFeignClient, times(1)).acqToDci(reqDTO);
        verify(authEncryptionFeignClient, never()).zpkFromLmkToZmk();
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 密钥服务异常")
    void testToDciAuthAuthentication_EncryptionServiceException() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024("814");
        
        when(authEncryptionFeignClient.zpkFromLmkToZmk()).thenThrow(new RuntimeException("Encryption service error"));
        
        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);
        });
        
        verify(authEncryptionFeignClient, times(1)).zpkFromLmkToZmk();
        verify(anyTechFeignClient, never()).acqToDci(any());
    }

    @Test
    @DisplayName("测试toDciAuthAuthentication方法 - 网关服务异常")
    void testToDciAuthAuthentication_GatewayServiceException() throws Exception {
        // Arrange
        DCIDcs8583ReqDTO reqDTO = new DCIDcs8583ReqDTO();
        reqDTO.setF024("640");
        
        when(anyTechFeignClient.acqToDci(reqDTO)).thenThrow(new RuntimeException("Gateway service error"));
        
        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            dciAuthenticationProcessService.toDciAuthAuthentication(reqDTO);
        });
        
        verify(anyTechFeignClient, times(1)).acqToDci(reqDTO);
    }
}
