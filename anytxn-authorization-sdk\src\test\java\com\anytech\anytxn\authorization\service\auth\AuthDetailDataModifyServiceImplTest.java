package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.service.auth.IAuthorizationLogService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.base.service.auth.IPreAuthorizationLogService;
import com.anytech.anytxn.authorization.service.manager.AuthCheckItemManager;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthDetailDataModifyServiceImpl单元测试类
 * 
 * <AUTHOR> Assistant
 * @datetime 2025/8/1
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权详细数据修改服务测试")
class AuthDetailDataModifyServiceImplTest {

    @Mock
    private IAuthorizationLogService authorizationLogService;
    
    @Mock
    private IPreAuthorizationLogService preAuthorizationLogService;
    
    @Mock
    private IOutstandingTransService outstandingTransService;
    
    @Mock
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;
    
    @Mock
    private AuthCheckItemManager authCheckItemManager;

    @InjectMocks
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    private AuthRecordedDTO authRecordedDTO;
    private AuthorizationCheckProcessingPayload payload;
    private AccountManagementInfoDTO accountManagementInfoDTO;
    private OrganizationInfoResDTO organizationInfoResDTO;
    private AuthorisationProcessingResDTO authorProcessInfo;

    @BeforeEach
    void setUp() {
        authRecordedDTO = createAuthRecordedDTO();
        payload = createAuthorizationCheckProcessingPayload();
        accountManagementInfoDTO = createAccountManagementInfoDTO();
        organizationInfoResDTO = createOrganizationInfoResDTO();
        authorProcessInfo = createAuthorisationProcessingResDTO();
    }

    @Test
    @DisplayName("测试modifyPreAuthorizationLog方法 - 正常情况")
    void testModifyPreAuthorizationLog_Success() {
        // Arrange
        authRecordedDTO.setAuthTransactionTypeCode("00"); // 非冲正、撤销交易
        when(preAuthorizationLogService.insert(any(), any())).thenReturn(1);

        // Act
        int result = authDetailDataModifyService.modifyPreAuthorizationLog(
            authRecordedDTO, accountManagementInfoDTO, organizationInfoResDTO, authorProcessInfo);

        // Assert
        assertEquals(1, result);
        verify(preAuthorizationLogService, times(1)).insert(any(), any());
    }

    @Test
    @DisplayName("测试modifyPreAuthorizationLog方法 - 冲正交易")
    void testModifyPreAuthorizationLog_ReversalTrans() {
        // Arrange
        authRecordedDTO.setAuthTransactionTypeCode("01"); // 冲正交易
        when(preAuthorizationLogService.insert(any(), any())).thenReturn(1);
        when(preAuthorizationLogService.updatePreAuthorizationLogByPrimaryId(any())).thenReturn(1);

        // Act
        int result = authDetailDataModifyService.modifyPreAuthorizationLog(
            authRecordedDTO, accountManagementInfoDTO, organizationInfoResDTO, authorProcessInfo);

        // Assert
        assertEquals(1, result);
        verify(preAuthorizationLogService, times(1)).insert(any(), any());
        verify(preAuthorizationLogService, times(1)).updatePreAuthorizationLogByPrimaryId(any());
    }

    @Test
    @DisplayName("测试modifyCardAuthorization方法 - 正常情况")
    void testModifyCardAuthorization_Success() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);
        when(cardAuthorizationInfoMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        // Act
        int result = authDetailDataModifyService.modifyCardAuthorization(payload);

        // Assert
        assertEquals(0, result); // 0表示成功
        assertNotNull(cardAuthorizationDTO.getUpdateTime());
        assertEquals(2L, cardAuthorizationDTO.getVersionNumber()); // 版本号+1
    }

    @Test
    @DisplayName("测试modifyCardAuthorization方法 - 更新失败")
    void testModifyCardAuthorization_UpdateFailed() {
        // Arrange
        CardAuthorizationDTO cardAuthorizationDTO = createCardAuthorizationDTO();
        payload.setCardAuthorizationDTO(cardAuthorizationDTO);
        when(cardAuthorizationInfoMapper.updateByPrimaryKeySelective(any())).thenReturn(0);

        // Act
        int result = authDetailDataModifyService.modifyCardAuthorization(payload);

        // Assert
        assertEquals(-2, result); // -2表示异常
    }

    @Test
    @DisplayName("测试limitUpdate方法 - 正常更新")
    void testLimitUpdate_Success() {
        // Arrange
        SystemTableDTO systemInfo = createSystemTableDTO();
        systemInfo.setLimitUpdateFlag("1"); // 开启额度更新
        payload.setSystemInfo(systemInfo);
        payload.setAuthRecordedDTO(authRecordedDTO);
        authRecordedDTO.setPreAuthComplete(false);
        authRecordedDTO.setPostingTransactionCodeRev(null);

        // Act
        authDetailDataModifyService.limitUpdate(payload);

        // Assert - 验证方法执行完成，没有抛出异常
        assertNotNull(payload.getSystemInfo());
    }

    @Test
    @DisplayName("测试limitUpdate方法 - 跳过更新")
    void testLimitUpdate_Skip() {
        // Arrange
        SystemTableDTO systemInfo = createSystemTableDTO();
        systemInfo.setLimitUpdateFlag("0"); // 关闭额度更新
        payload.setSystemInfo(systemInfo);
        payload.setAuthRecordedDTO(authRecordedDTO);
        authRecordedDTO.setPreAuthComplete(false);
        authRecordedDTO.setPostingTransactionCodeRev("REV001");

        // Act
        authDetailDataModifyService.limitUpdate(payload);

        // Assert - 验证方法执行完成，没有抛出异常
        assertNotNull(payload.getSystemInfo());
    }

    // 辅助方法
    private AuthRecordedDTO createAuthRecordedDTO() {
        AuthRecordedDTO dto = new AuthRecordedDTO();
        dto.setAuthGlobalFlowNumber("TEST_FLOW_NUMBER");
        dto.setAuthCardNumber("****************");
        dto.setAuthTransactionTypeCode("00");
        dto.setAuthTransactionAmount(BigDecimal.valueOf(10000));
        dto.setAuthCardholderBillingAmount(BigDecimal.valueOf(10000));
        dto.setOrganizationNumber("TEST_ORG");
        dto.setPreAuthComplete(false);
        return dto;
    }
    
    private AuthorizationCheckProcessingPayload createAuthorizationCheckProcessingPayload() {
        AuthorizationCheckProcessingPayload payload = new AuthorizationCheckProcessingPayload();
        payload.setAuthRecordedDTO(authRecordedDTO);
        return payload;
    }
    
    private AccountManagementInfoDTO createAccountManagementInfoDTO() {
        AccountManagementInfoDTO dto = new AccountManagementInfoDTO();
        dto.setAccountManagementId("TEST_ACCOUNT_ID");
        dto.setOrganizationNumber("TEST_ORG");
        return dto;
    }
    
    private OrganizationInfoResDTO createOrganizationInfoResDTO() {
        OrganizationInfoResDTO dto = new OrganizationInfoResDTO();
        dto.setOrganizationNumber("TEST_ORG");
        dto.setNextProcessingDay(LocalDate.now());
        return dto;
    }
    
    private AuthorisationProcessingResDTO createAuthorisationProcessingResDTO() {
        AuthorisationProcessingResDTO dto = new AuthorisationProcessingResDTO();
        dto.setAuthorisationRemainDays(30);
        return dto;
    }
    
    private CardAuthorizationDTO createCardAuthorizationDTO() {
        CardAuthorizationDTO dto = new CardAuthorizationDTO();
        dto.setCardNumber("****************");
        dto.setOrganizationNumber("TEST_ORG");
        dto.setVersionNumber(1L);
        dto.setUpdateTime(LocalDateTime.now());
        return dto;
    }
    
    private SystemTableDTO createSystemTableDTO() {
        SystemTableDTO dto = new SystemTableDTO();
        dto.setSystemId("0000");
        dto.setLimitUpdateFlag("1");
        return dto;
    }
}
