package com.anytech.anytxn.authorization.service.paramter;

import com.anytech.anytxn.authorization.base.domain.dto.AuthLogSearchKeyReqDTO;
import com.anytech.anytxn.authorization.base.domain.dto.AuthorizationLogResultDTO;
import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.mapper.cup.AuthorizationLogMapper;
import com.anytech.anytxn.authorization.mapper.cup.AuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.mapper.epcc.AuthorizationLogEpccMapper;
import com.anytech.anytxn.authorization.mapper.express.AuthorizationLogExpressMapper;
import com.anytech.anytxn.authorization.mapper.jcb.AuthorizationLogJcbMapper;
import com.anytech.anytxn.authorization.mapper.master.AuthorizationLogMcMapper;
import com.anytech.anytxn.authorization.mapper.visa.AuthorizationLogVisaMapper;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthorizationLogDTO;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmSysDictSelfMapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthorizationLogServiceImpl单元测试类
 *
 * <AUTHOR> Assistant
 * @datetime 2025/8/1
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权日志服务测试")
class AuthorizationLogServiceImplTest {

    @Mock
    private AuthorizationLogSelfMapper authorizationLogSelfMapper;

    @Mock
    private AuthorizationLogMapper authorizationLogMapper;

    @Mock
    private AuthorizationLogVisaMapper authorizationLogVisaMapper;

    @Mock
    private AuthorizationLogMcMapper authorizationLogMcMapper;

    @Mock
    private AuthorizationLogJcbMapper authorizationLogJcbMapper;

    @Mock
    private AuthorizationLogEpccMapper authorizationLogEpccMapper;

    @Mock
    private AuthorizationLogExpressMapper authorizationLogExpressMapper;

    @Mock
    private ParmSysDictSelfMapper parmSysDictSelfMapper;

    @InjectMocks
    private AuthorizationLogServiceImpl authorizationLogService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    @DisplayName("测试getByByAuthLogId方法 - 参数为空")
    void testGetByByAuthLogId_NullParameter() {
        // Act & Assert
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            authorizationLogService.getByByAuthLogId(null);
        });

        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, exception.getErrCode());
        assertEquals(AuthRepDetailEnum.AU_PI, exception.getErrDetail());
    }

    @Test
    @DisplayName("测试getByByAuthLogId方法 - 空字符串参数")
    void testGetByByAuthLogId_EmptyParameter() {
        // Act & Assert
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            authorizationLogService.getByByAuthLogId("");
        });

        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, exception.getErrCode());
        assertEquals(AuthRepDetailEnum.AU_PI, exception.getErrDetail());
    }

    @Test
    @DisplayName("测试getByByAuthLogId方法 - 正常查询")
    void testGetByByAuthLogId_Success() {
        // Arrange
        String authLogId = "AUTH_LOG_001";
        when(authorizationLogMapper.selectByPrimaryKey(authLogId)).thenReturn(null);
        when(authorizationLogVisaMapper.selectByPrimaryKey(authLogId)).thenReturn(null);
        when(authorizationLogMcMapper.selectByPrimaryKey(authLogId)).thenReturn(null);
        when(authorizationLogJcbMapper.selectByPrimaryKey(authLogId)).thenReturn(null);
        when(authorizationLogEpccMapper.selectByPrimaryKey(authLogId)).thenReturn(null);
        when(authorizationLogExpressMapper.selectByPrimaryKey(authLogId)).thenReturn(null);

        // Act
        AuthorizationLogResultDTO result = authorizationLogService.getByByAuthLogId(authLogId);

        // Assert
        assertNotNull(result);
        verify(authorizationLogMapper, times(1)).selectByPrimaryKey(authLogId);
    }

    @Test
    @DisplayName("测试insert方法 - 参数为空")
    void testInsert_NullParameter() {
        // Act & Assert
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            authorizationLogService.insert(null);
        });

        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, exception.getErrCode());
        assertEquals(AuthRepDetailEnum.AU_PI_E2, exception.getErrDetail());
    }

    @Test
    @DisplayName("测试getAuthLogList方法 - 卡号为空")
    void testGetAuthLogList_EmptyCardNumber() {
        // Act & Assert
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            authorizationLogService.getAuthLogList("");
        });

        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, exception.getErrCode());
        assertEquals(AuthRepDetailEnum.Q_AU_FL, exception.getErrDetail());
    }

    @Test
    @DisplayName("测试getAuthLogList方法 - 卡号为null")
    void testGetAuthLogList_NullCardNumber() {
        // Act & Assert
        AnyTxnAuthException exception = assertThrows(AnyTxnAuthException.class, () -> {
            authorizationLogService.getAuthLogList(null);
        });

        assertEquals(AnyTxnAuthRespCodeEnum.P_PARAM_IS_NULL, exception.getErrCode());
        assertEquals(AuthRepDetailEnum.Q_AU_FL, exception.getErrDetail());
    }

}
