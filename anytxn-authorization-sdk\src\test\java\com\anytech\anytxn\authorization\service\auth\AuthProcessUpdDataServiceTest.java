package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @description AuthProcessUpdDataService接口的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthProcessUpdDataService 授权处理更新数据服务接口测试")
class AuthProcessUpdDataServiceTest {

    @Mock
    private AuthProcessUpdDataService authProcessUpdDataService;

    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        when(OrgNumberUtils.getOrg()).thenReturn("001");
        when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 创建授权检查处理载荷
        authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();

        AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("1000"));
        authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
    }

    @Test
    @DisplayName("测试updateData方法-成功更新")
    void testUpdateData_Success() {
        // Arrange
        int expectedResult = 0; // 成功
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(expectedResult);

        // Act
        int result = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        verify(authProcessUpdDataService).updateData(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试updateData方法-更新失败")
    void testUpdateData_Failure() {
        // Arrange
        int expectedResult = 1; // 更新失败
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(expectedResult);

        // Act
        int result = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        verify(authProcessUpdDataService).updateData(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试updateData方法-系统错误")
    void testUpdateData_SystemError() {
        // Arrange
        int expectedResult = -1; // 系统错误
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(expectedResult);

        // Act
        int result = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        verify(authProcessUpdDataService).updateData(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试updateData方法-空参数")
    void testUpdateData_NullParameter() {
        // Arrange
        int expectedResult = -1; // 参数错误
        when(authProcessUpdDataService.updateData(isNull()))
                .thenReturn(expectedResult);

        // Act
        int result = authProcessUpdDataService.updateData(null);

        // Assert
        assertEquals(expectedResult, result);
        verify(authProcessUpdDataService).updateData(isNull());
    }

    @Test
    @DisplayName("测试updateData方法-抛出异常")
    void testUpdateData_ThrowsException() {
        // Arrange
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenThrow(new RuntimeException("数据更新异常"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        });

        verify(authProcessUpdDataService).updateData(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试updateData方法-多次调用")
    void testUpdateData_MultipleCalls() {
        // Arrange
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0, 1, -1); // 依次返回不同结果

        // Act & Assert
        assertEquals(0, authProcessUpdDataService.updateData(authorizationCheckProcessingPayload));
        assertEquals(1, authProcessUpdDataService.updateData(authorizationCheckProcessingPayload));
        assertEquals(-1, authProcessUpdDataService.updateData(authorizationCheckProcessingPayload));

        // Verify
        verify(authProcessUpdDataService, times(3))
                .updateData(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试updateData方法-不同载荷参数")
    void testUpdateData_DifferentPayloads() {
        // Arrange
        AuthorizationCheckProcessingPayload anotherPayload = new AuthorizationCheckProcessingPayload();
        AuthRecordedDTO anotherAuthDTO = new AuthRecordedDTO();
        anotherAuthDTO.setAuthCardNumber("9876543210987654");
        anotherAuthDTO.setAuthTransactionAmount(new BigDecimal("2000"));
        anotherPayload.setAuthRecordedDTO(anotherAuthDTO);
        
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0);

        // Act
        int result1 = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        int result2 = authProcessUpdDataService.updateData(anotherPayload);

        // Assert
        assertEquals(0, result1);
        assertEquals(0, result2);
        verify(authProcessUpdDataService).updateData(eq(authorizationCheckProcessingPayload));
        verify(authProcessUpdDataService).updateData(eq(anotherPayload));
    }

    @Test
    @DisplayName("测试updateData方法-验证方法签名")
    void testUpdateData_MethodSignature() {
        // Arrange
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0);

        // Act
        int result = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertNotNull(result);
        assertTrue(result >= -1 && result <= 1); // 合理的返回值范围
        
        // 验证方法被正确调用
        verify(authProcessUpdDataService).updateData(
                argThat(payload -> payload != null && payload.getAuthRecordedDTO() != null)
        );
    }

    @Test
    @DisplayName("测试updateData方法-验证返回值类型")
    void testUpdateData_ReturnType() {
        // Arrange
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0);

        // Act
        int result = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertTrue(Integer.valueOf(result) instanceof Integer);
        assertEquals(0, result);
        verify(authProcessUpdDataService).updateData(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试updateData方法-数据库连接异常")
    void testUpdateData_DatabaseException() {
        // Arrange
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        });

        assertEquals("数据库连接异常", exception.getMessage());
        verify(authProcessUpdDataService).updateData(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试updateData方法-验证调用次数")
    void testUpdateData_VerifyInvocations() {
        // Arrange
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0);

        // Act
        authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        verify(authProcessUpdDataService, times(2)).updateData(eq(authorizationCheckProcessingPayload));
        verify(authProcessUpdDataService, atLeast(1)).updateData(any(AuthorizationCheckProcessingPayload.class));
        verify(authProcessUpdDataService, atMost(3)).updateData(any(AuthorizationCheckProcessingPayload.class));
    }

    @Test
    @DisplayName("测试updateData方法-边界值测试")
    void testUpdateData_BoundaryValues() {
        // Arrange
        when(authProcessUpdDataService.updateData(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(Integer.MAX_VALUE, Integer.MIN_VALUE);

        // Act
        int maxResult = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);
        int minResult = authProcessUpdDataService.updateData(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(Integer.MAX_VALUE, maxResult);
        assertEquals(Integer.MIN_VALUE, minResult);
        verify(authProcessUpdDataService, times(2))
                .updateData(eq(authorizationCheckProcessingPayload));
    }
} 