package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.base.service.auth.IAuthPrePostLogService;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthPrePostLogDTO;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import com.anytech.anytxn.business.base.authorization.enums.AuthRepDetailEnum;
import com.anytech.anytxn.business.dao.authorization.mapper.AuthPrePostLogMapper;
import com.anytech.anytxn.business.dao.authorization.mapper.AuthPrePostLogSelfMapper;
import com.anytech.anytxn.business.dao.authorization.model.AuthPrePostLog;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * @description AuthPrePostLogServiceImpl的单元测试类
 * <AUTHOR>
 * @date 2025/07/07
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("授权前后日志服务测试")
class AuthPrePostLogServiceImplTest {

    @Mock
    private AuthPrePostLogMapper authPrePostLogMapper;

    @Mock
    private AuthPrePostLogSelfMapper selfMapper;

    @InjectMocks
    private AuthPrePostLogServiceImpl authPrePostLogService;

    private AuthPrePostLogDTO authPrePostLogDTO;
    private AuthPrePostLog authPrePostLog;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("001");
        lenient().when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 初始化测试数据
        authPrePostLogDTO = new AuthPrePostLogDTO();
        authPrePostLogDTO.setId("123456789");
        authPrePostLogDTO.setGlobalFlowNumber("GLOBAL123456");
        authPrePostLogDTO.setOrganizationNumber("ORG001");
        // 移除不存在的方法调用
        authPrePostLogDTO.setCreateTime(LocalDateTime.now());

        authPrePostLog = new AuthPrePostLog();
        authPrePostLog.setId("123456789");
        authPrePostLog.setGlobalFlowNumber("GLOBAL123456");
        authPrePostLog.setOrganizationNumber("ORG001");
        // 移除不存在的方法调用
        authPrePostLog.setCreateTime(LocalDateTime.now());
    }

    @Test
    @DisplayName("插入操作 - 成功插入授权前后日志")
    void insert_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            beanMapping.when(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class))
                    .thenReturn(authPrePostLog);
            when(authPrePostLogMapper.insertSelective(any(AuthPrePostLog.class))).thenReturn(1);

            // Act
            int result = authPrePostLogService.insert(authPrePostLogDTO);

            // Assert
            assertThat(result).isEqualTo(1);
            verify(authPrePostLogMapper).insertSelective(authPrePostLog);
            beanMapping.verify(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class));
        }
    }

    @Test
    @DisplayName("插入操作 - 参数为null时抛出异常")
    void insert_NullParameter_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> authPrePostLogService.insert(null))
                .isInstanceOf(AnyTxnAuthException.class)
                .hasMessageContaining("outstandingTransactionDTO is null");

        verify(authPrePostLogMapper, never()).insertSelective(any());
    }

    @Test
    @DisplayName("插入操作 - 数据库插入失败时抛出异常")
    void insert_DatabaseInsertFailed_ThrowsException() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            beanMapping.when(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class))
                    .thenReturn(authPrePostLog);
            when(authPrePostLogMapper.insertSelective(any(AuthPrePostLog.class))).thenReturn(0);

            // Act & Assert
            assertThatThrownBy(() -> authPrePostLogService.insert(authPrePostLogDTO))
                    .isInstanceOf(AnyTxnAuthException.class);

            verify(authPrePostLogMapper).insertSelective(authPrePostLog);
        }
    }

    @Test
    @DisplayName("插入操作 - 数据库操作异常时抛出异常")
    void insert_DatabaseException_ThrowsException() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            beanMapping.when(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class))
                    .thenReturn(authPrePostLog);
            when(authPrePostLogMapper.insertSelective(any(AuthPrePostLog.class)))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // Act & Assert
            assertThatThrownBy(() -> authPrePostLogService.insert(authPrePostLogDTO))
                    .isInstanceOf(AnyTxnAuthException.class);

            verify(authPrePostLogMapper).insertSelective(authPrePostLog);
        }
    }

    @Test
    @DisplayName("查询操作 - 成功根据全局流水号和机构号查询")
    void queryByGloAndOrg_Success() {
        // Arrange
        List<AuthPrePostLog> authPrePostLogList = new ArrayList<>();
        authPrePostLogList.add(authPrePostLog);
        
        List<AuthPrePostLogDTO> expectedDTOList = new ArrayList<>();
        expectedDTOList.add(authPrePostLogDTO);

        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            when(selfMapper.queryByGloAndOrg("GLOBAL123456", "ORG001"))
                    .thenReturn(authPrePostLogList);
            beanMapping.when(() -> BeanMapping.copyList(authPrePostLogList, AuthPrePostLogDTO.class))
                    .thenReturn(expectedDTOList);

            // Act
            List<AuthPrePostLogDTO> result = authPrePostLogService.queryByGloAndOrg("GLOBAL123456", "ORG001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getId()).isEqualTo("123456789");
            assertThat(result.get(0).getGlobalFlowNumber()).isEqualTo("GLOBAL123456");

            verify(selfMapper).queryByGloAndOrg("GLOBAL123456", "ORG001");
            beanMapping.verify(() -> BeanMapping.copyList(authPrePostLogList, AuthPrePostLogDTO.class));
        }
    }

    @Test
    @DisplayName("查询操作 - 只传入全局流水号查询成功")
    void queryByGloAndOrg_OnlyGlobalFlowNumber_Success() {
        // Arrange
        List<AuthPrePostLog> authPrePostLogList = new ArrayList<>();
        authPrePostLogList.add(authPrePostLog);
        
        List<AuthPrePostLogDTO> expectedDTOList = new ArrayList<>();
        expectedDTOList.add(authPrePostLogDTO);

        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            when(selfMapper.queryByGloAndOrg("GLOBAL123456", null))
                    .thenReturn(authPrePostLogList);
            beanMapping.when(() -> BeanMapping.copyList(authPrePostLogList, AuthPrePostLogDTO.class))
                    .thenReturn(expectedDTOList);

            // Act
            List<AuthPrePostLogDTO> result = authPrePostLogService.queryByGloAndOrg("GLOBAL123456", null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);

            verify(selfMapper).queryByGloAndOrg("GLOBAL123456", null);
        }
    }

    @Test
    @DisplayName("查询操作 - 查询结果为空时返回null")
    void queryByGloAndOrg_NoResults_ReturnsNull() {
        // Arrange
        when(selfMapper.queryByGloAndOrg("GLOBAL123456", "ORG001")).thenReturn(null);

        // Act
        List<AuthPrePostLogDTO> result = authPrePostLogService.queryByGloAndOrg("GLOBAL123456", "ORG001");

        // Assert
        assertThat(result).isNull();
        verify(selfMapper).queryByGloAndOrg("GLOBAL123456", "ORG001");
    }

    @Test
    @DisplayName("查询操作 - 全局流水号为空时抛出异常")
    void queryByGloAndOrg_EmptyGlobalFlowNumber_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> authPrePostLogService.queryByGloAndOrg("", "ORG001"))
                .isInstanceOf(AnyTxnAuthException.class)
                .hasMessageContaining("globalFlowNumber is null");

        verify(selfMapper, never()).queryByGloAndOrg(anyString(), anyString());
    }

    @Test
    @DisplayName("查询操作 - 全局流水号为null时抛出异常")
    void queryByGloAndOrg_NullGlobalFlowNumber_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> authPrePostLogService.queryByGloAndOrg(null, "ORG001"))
                .isInstanceOf(AnyTxnAuthException.class)
                .hasMessageContaining("globalFlowNumber is null");

        verify(selfMapper, never()).queryByGloAndOrg(anyString(), anyString());
    }

    @Test
    @DisplayName("查询操作 - 数据库操作异常时抛出异常")
    void queryByGloAndOrg_DatabaseException_ThrowsException() {
        // Arrange
        when(selfMapper.queryByGloAndOrg("GLOBAL123456", "ORG001"))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        assertThatThrownBy(() -> authPrePostLogService.queryByGloAndOrg("GLOBAL123456", "ORG001"))
                .isInstanceOf(AnyTxnAuthException.class);

        verify(selfMapper).queryByGloAndOrg("GLOBAL123456", "ORG001");
    }

    @Test
    @DisplayName("更新操作 - 成功更新授权前后日志")
    void update_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            beanMapping.when(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class))
                    .thenReturn(authPrePostLog);
            when(authPrePostLogMapper.updateByPrimaryKeySelective(any(AuthPrePostLog.class))).thenReturn(1);

            // Act
            int result = authPrePostLogService.update(authPrePostLogDTO);

            // Assert
            assertThat(result).isEqualTo(1);
            verify(authPrePostLogMapper).updateByPrimaryKeySelective(any(AuthPrePostLog.class));
            beanMapping.verify(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class));
        }
    }

    @Test
    @DisplayName("更新操作 - 参数为null时抛出异常")
    void update_NullParameter_ThrowsException() {
        // Act & Assert
        assertThatThrownBy(() -> authPrePostLogService.update(null))
                .isInstanceOf(AnyTxnAuthException.class)
                .hasMessageContaining("authPrePostLogDTO is null!");

        verify(authPrePostLogMapper, never()).updateByPrimaryKeySelective(any());
    }

    @Test
    @DisplayName("更新操作 - 数据库更新失败时抛出异常")
    void update_DatabaseUpdateFailed_ThrowsException() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            beanMapping.when(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class))
                    .thenReturn(authPrePostLog);
            when(authPrePostLogMapper.updateByPrimaryKeySelective(any(AuthPrePostLog.class))).thenReturn(0);

            // Act & Assert
            assertThatThrownBy(() -> authPrePostLogService.update(authPrePostLogDTO))
                    .isInstanceOf(AnyTxnAuthException.class);

            verify(authPrePostLogMapper).updateByPrimaryKeySelective(any(AuthPrePostLog.class));
        }
    }

    @Test
    @DisplayName("更新操作 - 数据库操作异常时抛出异常")
    void update_DatabaseException_ThrowsException() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            beanMapping.when(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class))
                    .thenReturn(authPrePostLog);
            when(authPrePostLogMapper.updateByPrimaryKeySelective(any(AuthPrePostLog.class)))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // Act & Assert
            assertThatThrownBy(() -> authPrePostLogService.update(authPrePostLogDTO))
                    .isInstanceOf(AnyTxnAuthException.class);

            verify(authPrePostLogMapper).updateByPrimaryKeySelective(any(AuthPrePostLog.class));
        }
    }

    @Test
    @DisplayName("更新操作 - 验证更新时间被正确设置")
    void update_UpdateTimeSet_Success() {
        // Arrange
        AuthPrePostLog capturedLog = new AuthPrePostLog();
        
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            beanMapping.when(() -> BeanMapping.copy(authPrePostLogDTO, AuthPrePostLog.class))
                    .thenReturn(capturedLog);
            when(authPrePostLogMapper.updateByPrimaryKeySelective(any(AuthPrePostLog.class))).thenReturn(1);

            // Act
            authPrePostLogService.update(authPrePostLogDTO);

            // Assert
            assertThat(capturedLog.getUpdateTime()).isNotNull();
            assertThat(capturedLog.getUpdateTime()).isBeforeOrEqualTo(LocalDateTime.now());
            verify(authPrePostLogMapper).updateByPrimaryKeySelective(capturedLog);
        }
    }
} 