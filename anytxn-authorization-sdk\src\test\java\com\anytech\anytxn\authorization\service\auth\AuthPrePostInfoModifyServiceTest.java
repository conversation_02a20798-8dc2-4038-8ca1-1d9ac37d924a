package com.anytech.anytxn.authorization.service.auth;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * @description AuthPrePostInfoModifyService接口的单元测试类
 * <AUTHOR>
 * @date 2025/07/08
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthPrePostInfoModifyService 授权前后信息修改服务接口测试")
class AuthPrePostInfoModifyServiceTest {

    @Mock
    private AuthPrePostInfoModifyService authPrePostInfoModifyService;

    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        when(OrgNumberUtils.getOrg()).thenReturn("001");
        when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 创建授权检查处理载荷
        authorizationCheckProcessingPayload = new AuthorizationCheckProcessingPayload();

        AuthRecordedDTO authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("1000"));
        authorizationCheckProcessingPayload.setAuthRecordedDTO(authRecordedDTO);
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-成功修改")
    void testModifyAuthPrePostInfo_Success() {
        // Arrange
        int expectedResult = 0; // 成功
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(expectedResult);

        // Act
        int result = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-修改失败")
    void testModifyAuthPrePostInfo_Failure() {
        // Arrange
        int expectedResult = 1; // 修改失败
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(expectedResult);

        // Act
        int result = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-系统错误")
    void testModifyAuthPrePostInfo_SystemError() {
        // Arrange
        int expectedResult = -1; // 系统错误
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(expectedResult);

        // Act
        int result = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(expectedResult, result);
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-空参数")
    void testModifyAuthPrePostInfo_NullParameter() {
        // Arrange
        int expectedResult = -1; // 参数错误
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(isNull()))
                .thenReturn(expectedResult);

        // Act
        int result = authPrePostInfoModifyService.modifyAuthPrePostInfo(null);

        // Assert
        assertEquals(expectedResult, result);
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(isNull());
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-抛出异常")
    void testModifyAuthPrePostInfo_ThrowsException() {
        // Arrange
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenThrow(new RuntimeException("修改信息异常"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);
        });

        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-多次调用")
    void testModifyAuthPrePostInfo_MultipleCalls() {
        // Arrange
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0, 1, -1); // 依次返回不同结果

        // Act & Assert
        assertEquals(0, authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload));
        assertEquals(1, authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload));
        assertEquals(-1, authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload));

        // Verify
        verify(authPrePostInfoModifyService, times(3))
                .modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-不同载荷参数")
    void testModifyAuthPrePostInfo_DifferentPayloads() {
        // Arrange
        AuthorizationCheckProcessingPayload anotherPayload = new AuthorizationCheckProcessingPayload();
        AuthRecordedDTO anotherAuthDTO = new AuthRecordedDTO();
        anotherAuthDTO.setAuthCardNumber("9876543210987654");
        anotherAuthDTO.setAuthTransactionAmount(new BigDecimal("2000"));
        anotherPayload.setAuthRecordedDTO(anotherAuthDTO);
        
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0);

        // Act
        int result1 = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);
        int result2 = authPrePostInfoModifyService.modifyAuthPrePostInfo(anotherPayload);

        // Assert
        assertEquals(0, result1);
        assertEquals(0, result2);
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(eq(anotherPayload));
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-验证方法签名")
    void testModifyAuthPrePostInfo_MethodSignature() {
        // Arrange
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0);

        // Act
        int result = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);

        // Assert
        assertNotNull(result);
        assertTrue(result >= -1 && result <= 1); // 合理的返回值范围
        
        // 验证方法被正确调用
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(
                argThat(payload -> payload != null && payload.getAuthRecordedDTO() != null)
        );
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-验证返回值类型")
    void testModifyAuthPrePostInfo_ReturnType() {
        // Arrange
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(0);

        // Act
        int result = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);

        // Assert
        assertTrue(Integer.valueOf(result) instanceof Integer);
        assertEquals(0, result);
        verify(authPrePostInfoModifyService).modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
    }

    @Test
    @DisplayName("测试modifyAuthPrePostInfo方法-边界值测试")
    void testModifyAuthPrePostInfo_BoundaryValues() {
        // Arrange
        when(authPrePostInfoModifyService.modifyAuthPrePostInfo(any(AuthorizationCheckProcessingPayload.class)))
                .thenReturn(Integer.MAX_VALUE, Integer.MIN_VALUE);

        // Act
        int maxResult = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);
        int minResult = authPrePostInfoModifyService.modifyAuthPrePostInfo(authorizationCheckProcessingPayload);

        // Assert
        assertEquals(Integer.MAX_VALUE, maxResult);
        assertEquals(Integer.MIN_VALUE, minResult);
        verify(authPrePostInfoModifyService, times(2))
                .modifyAuthPrePostInfo(eq(authorizationCheckProcessingPayload));
    }
} 