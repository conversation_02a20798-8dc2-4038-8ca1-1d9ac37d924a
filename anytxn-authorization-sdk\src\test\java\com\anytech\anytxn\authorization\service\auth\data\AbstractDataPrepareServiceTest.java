package com.anytech.anytxn.authorization.service.auth.data;

import com.anytech.anytxn.authorization.base.exception.AnyTxnAuthException;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.enums.CardClassEnum;
import com.anytech.anytxn.authorization.base.service.manager.ApplicationManager;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.manager.MastercAuthDataUpdateManager;
import com.anytech.anytxn.authorization.utils.AuthAssert;
import com.anytech.anytxn.business.base.account.domain.dto.AccountManagementInfoDTO;
import com.anytech.anytxn.business.base.account.domain.dto.CommonAccountDTO;
import com.anytech.anytxn.business.dao.account.model.AccountManagementInfo;
import com.anytech.anytxn.business.account.service.CommonAccountService;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.dao.card.mapper.CardAuthorizationInfoMapper;
import com.anytech.anytxn.business.common.service.SharedInfoFindServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthorizationRuleDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTypeDetailCodeDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTypeDetailCodeReqDTO;
import com.anytech.anytxn.parameter.base.authorization.service.IAuthorizationRuleService;
import com.anytech.anytxn.parameter.base.authorization.service.IParmTypeDetailCodeService;
import com.anytech.anytxn.parameter.base.account.domain.dto.AuthorisationProcessingResDTO;
import com.anytech.anytxn.parameter.base.account.service.IAuthorisationProcessingService;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.base.common.domain.dto.product.CardProductInfoResDTO;
import com.anytech.anytxn.parameter.base.common.service.ITransactionCodeService;
import com.anytech.anytxn.parameter.base.common.service.product.ICardProductInfoService;
import com.anytech.anytxn.parameter.base.common.domain.dto.TransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.IOrganizationInfoService;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.ApplicationArguments;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AbstractDataPrepareService单元测试类
 *
 * <AUTHOR> Assistant
 * @datetime 2025/8/1
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("抽象数据准备服务测试")
class AbstractDataPrepareServiceTest {

    @Mock
    private IAuthorizationRuleService authorizationRuleService;

    @Mock
    private ICardProductInfoService cardProductInfoService;

    @Mock
    private CardAuthorizationInfoMapper cardAuthorizationInfoMapper;

    @Mock
    private IOrganizationInfoService organizationInfoService;

    @Mock
    private ISystemTableService iSystemTableService;

    @Mock
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;

    @Mock
    private ParmCurrencyRateSelfMapper parmCurrencyRateSelfMapper;

    @Mock
    private CommonAccountService commonAccountService;

    @Mock
    private IAuthorisationProcessingService authorisationProcessingService;

    @Mock
    private IParmTypeDetailCodeService parmTypeDetailCodeService;

    @Mock
    private ITransactionCodeService transactionCodeService;

    @Mock
    private SharedInfoFindServiceImpl sharedInfoFindService;

    @Mock
    private AuthCheckManager authCheckManager;

    @Mock
    private MastercAuthDataUpdateManager mastercAuthDataUpdateManager;

    @Mock
    private ExchangeRateService exchangeRateService;

    @Mock
    private DefaultExchangeRateServiceImpl defaultExchangeRateService;

    // 创建一个具体的测试实现类
    private TestableAbstractDataPrepareService testableService;

    @BeforeEach
    void setUp() {
        testableService = new TestableAbstractDataPrepareService();
        // 使用反射设置私有字段
        setPrivateField(testableService, "authorizationRuleService", authorizationRuleService);
        setPrivateField(testableService, "cardProductInfoService", cardProductInfoService);
        setPrivateField(testableService, "cardAuthorizationInfoMapper", cardAuthorizationInfoMapper);
        setPrivateField(testableService, "organizationInfoService", organizationInfoService);
        setPrivateField(testableService, "iSystemTableService", iSystemTableService);
        setPrivateField(testableService, "parmCardCurrencyInfoSelfMapper", parmCardCurrencyInfoSelfMapper);
        setPrivateField(testableService, "parmCurrencyRateSelfMapper", parmCurrencyRateSelfMapper);
        setPrivateField(testableService, "commonAccountService", commonAccountService);
        setPrivateField(testableService, "authorisationProcessingService", authorisationProcessingService);
        setPrivateField(testableService, "parmTypeDetailCodeService", parmTypeDetailCodeService);
        setPrivateField(testableService, "transactionCodeService", transactionCodeService);
        setPrivateField(testableService, "sharedInfoFindService", sharedInfoFindService);
        setPrivateField(testableService, "authCheckManager", authCheckManager);
        setPrivateField(testableService, "mastercAuthDataUpdateManager", mastercAuthDataUpdateManager);
        setPrivateField(testableService, "exchangeRateService", exchangeRateService);
    }

    @Test
    @DisplayName("测试setExchangeRateService方法 - 正常设置")
    void testSetExchangeRateService_Success() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Arrange
            ExchangeRateService newExchangeRateService = mock(ExchangeRateService.class);

            // Act
            testableService.setExchangeRateService(newExchangeRateService);

            // Assert
            assertEquals(newExchangeRateService, testableService.exchangeRateService);
        }
    }

    @Test
    @DisplayName("测试setExchangeRateService方法 - 设置null值")
    void testSetExchangeRateService_NullValue() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Act
            testableService.setExchangeRateService(null);

            // Assert
            assertNull(testableService.exchangeRateService);
        }
    }

    @Test
    @DisplayName("测试getCustomerInfo方法 - 正常情况")
    void testGetCustomerInfo_Success() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Arrange
            AuthRecordedDTO authRecordedDTO = createAuthRecordedDTO();
            AuthorizationCheckProcessingPayload payload = createAuthorizationCheckProcessingPayload();
            CustomerAuthorizationInfoDTO customerInfo = createCustomerAuthorizationInfoDTO();

            when(sharedInfoFindService.getCustomerAuthorizationByCustomerId(anyString()))
                .thenReturn(customerInfo);

            // Act
            testableService.getCustomerInfo(authRecordedDTO, payload);

            // Assert
            assertEquals("TEST_CUSTOMER_ID", authRecordedDTO.getAuthCustomerId());
            assertEquals(customerInfo, payload.getCustomerAuthorizationInfoDTO());
            verify(sharedInfoFindService, times(1)).getCustomerAuthorizationByCustomerId("TEST_CUSTOMER_ID");
            verify(mastercAuthDataUpdateManager, times(1)).initLimitCustAccountBO(payload);
        }
    }

    @Test
    @DisplayName("测试getCustomerInfo方法 - 客户信息不存在")
    void testGetCustomerInfo_CustomerNotFound() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Arrange
            AuthRecordedDTO authRecordedDTO = createAuthRecordedDTO();
            AuthorizationCheckProcessingPayload payload = createAuthorizationCheckProcessingPayload();

            when(sharedInfoFindService.getCustomerAuthorizationByCustomerId(anyString()))
                .thenReturn(null);

            // Act & Assert
            assertThrows(AnyTxnAuthException.class, () -> {
                testableService.getCustomerInfo(authRecordedDTO, payload);
            });

            verify(sharedInfoFindService, times(1)).getCustomerAuthorizationByCustomerId("TEST_CUSTOMER_ID");
            verify(mastercAuthDataUpdateManager, never()).initLimitCustAccountBO(any());
        }
    }

    @Test
    @DisplayName("测试getAcctInfo方法 - 正常情况")
    void testGetAcctInfo_Success() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Arrange
            AuthRecordedDTO authRecordedDTO = createAuthRecordedDTO();
            AuthorizationCheckProcessingPayload payload = createAuthorizationCheckProcessingPayload();
            payload.setCardProductInfo(createCardProductInfoResDTO());

            List<AccountManagementInfo> accountInfos = createAccountManagementInfoList();
            when(commonAccountService.selectByCustomerIdAndOrg(any(CommonAccountDTO.class)))
                .thenReturn(accountInfos);

            // Act
            testableService.getAcctInfo(authRecordedDTO, payload);

            // Assert
            assertNotNull(payload.getAccountManagementInfoDTO());
            assertEquals("TEST_PRODUCT", authRecordedDTO.getAcctProductCode());
            verify(commonAccountService, times(1)).selectByCustomerIdAndOrg(any(CommonAccountDTO.class));
        }
    }

    @Test
    @DisplayName("测试getAcctInfo方法 - 主客户号为空")
    void testGetAcctInfo_EmptyCustomerId() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Arrange
            AuthRecordedDTO authRecordedDTO = createAuthRecordedDTO();
            AuthorizationCheckProcessingPayload payload = createAuthorizationCheckProcessingPayload();
            payload.getCardAuthorizationDTO().setPrimaryCustomerId("");

            // Act & Assert
            assertThrows(AnyTxnAuthException.class, () -> {
                testableService.getAcctInfo(authRecordedDTO, payload);
            });
        }
    }

    @Test
    @DisplayName("测试getCardInfo方法 - 验证基本调用")
    void testGetCardInfo_BasicCall() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("TEST_ORG");

            // Arrange
            AuthRecordedDTO authRecordedDTO = createAuthRecordedDTO();
            AuthorizationCheckProcessingPayload payload = createAuthorizationCheckProcessingPayload();

            // 简单的mock，避免复杂的对象创建
            when(organizationInfoService.findOrganizationInfo(anyString())).thenReturn(null);

            // Act & Assert - 期望抛出异常，因为organizationInfo为null
            assertThrows(AnyTxnAuthException.class, () -> {
                testableService.getCardInfo(authRecordedDTO, payload);
            });

            // 验证方法被调用
            verify(organizationInfoService, times(1)).findOrganizationInfo(anyString());
        }
    }

    @Test
    @DisplayName("测试run方法 - ApplicationRunner接口实现")
    void testRun_Success() throws Exception {
        try (MockedStatic<ApplicationManager> applicationManagerMock = mockStatic(ApplicationManager.class)) {
            // Arrange
            org.springframework.context.ApplicationContext mockContext = mock(org.springframework.context.ApplicationContext.class);
            // 直接设置静态字段
            ApplicationManager.applicationContext = mockContext;
            when(mockContext.getBean(DefaultExchangeRateServiceImpl.class)).thenReturn(defaultExchangeRateService);

            ApplicationArguments args = mock(ApplicationArguments.class);

            // Act
            testableService.run(args);

            // Assert
            assertEquals(defaultExchangeRateService, testableService.exchangeRateService);
        }
    }

    // 测试实现类
    private static class TestableAbstractDataPrepareService extends AbstractDataPrepareService {
        @Override
        public AuthorizationCheckProcessingPayload prepareAuthData(AuthRecordedDTO authRecordedDTO) {
            return new AuthorizationCheckProcessingPayload();
        }
    }

    // 辅助方法
    private void setPrivateField(Object target, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = target.getClass().getSuperclass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(target, value);
        } catch (Exception e) {
            // 忽略异常，某些字段可能不存在
        }
    }

    private AuthRecordedDTO createAuthRecordedDTO() {
        AuthRecordedDTO dto = new AuthRecordedDTO();
        dto.setAuthGlobalFlowNumber("TEST_FLOW_NUMBER");
        dto.setAuthCardNumber("1234567890123456");
        dto.setAuthTransactionTypeTopCode("00");
        dto.setAuthBillingCurrencyCode("USD");
        dto.setAuthTransactionCurrencyCode("USD");
        dto.setAuthTransactionAmount(java.math.BigDecimal.valueOf(10000));
        dto.setAuthCardholderBillingAmount(java.math.BigDecimal.valueOf(10000));
        dto.setOrganizationNumber("TEST_ORG");
        dto.setCardAuthorizationDTO(createCardAuthorizationDTO());
        return dto;
    }

    private CardAuthorizationDTO createCardAuthorizationDTO() {
        CardAuthorizationDTO dto = new CardAuthorizationDTO();
        dto.setPrimaryCustomerId("TEST_CUSTOMER_ID");
        dto.setOrganizationNumber("TEST_ORG");
        dto.setProductNumber("TEST_PRODUCT");
        dto.setCardNumber("1234567890123456");
        dto.setExpireDate("2512");
        return dto;
    }

    private AuthorizationCheckProcessingPayload createAuthorizationCheckProcessingPayload() {
        AuthorizationCheckProcessingPayload payload = new AuthorizationCheckProcessingPayload();
        payload.setCardAuthorizationDTO(createCardAuthorizationDTO());
        return payload;
    }

    private CustomerAuthorizationInfoDTO createCustomerAuthorizationInfoDTO() {
        CustomerAuthorizationInfoDTO dto = new CustomerAuthorizationInfoDTO();
        dto.setCustomerId("TEST_CUSTOMER_ID");
        dto.setOrganizationNumber("TEST_ORG");
        return dto;
    }

    private List<AccountManagementInfo> createAccountManagementInfoList() {
        List<AccountManagementInfo> list = new ArrayList<>();
        AccountManagementInfo info = new AccountManagementInfo();
        info.setOrganizationNumber("TEST_ORG");
        info.setProductNumber("TEST_PRODUCT");
        info.setCurrency("USD");
        info.setAccountManagementId("TEST_ACCOUNT_ID");
        list.add(info);
        return list;
    }

    private CardProductInfoResDTO createCardProductInfoResDTO() {
        CardProductInfoResDTO dto = new CardProductInfoResDTO();
        dto.setAccountProductNumber("TEST_PRODUCT");
        dto.setAuthCtlTableId("TEST_AUTH_TABLE_ID");
        dto.setAuthorisationProcessingId("TEST_AUTH_PROCESSING_ID");
        dto.setCardClass("1");
        dto.setSingleCurrencySettlInd("0");
        return dto;
    }


}