package com.anytech.anytxn.authorization.service.channel.dci.type;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.domain.model.AuthorizationLog;
import com.anytech.anytxn.authorization.base.enums.AuthMatchResultEnum;
import com.anytech.anytxn.authorization.base.service.auth.IOriginTransMatchProcessService;
import com.anytech.anytxn.authorization.base.service.auth.IOutstandingTransService;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.mapper.cup.AuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.service.auth.*;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.manager.AuthSmsManager;
import com.anytech.anytxn.authorization.service.manager.AuthThreadLocalManager;
import com.anytech.anytxn.authorization.service.rule.RuleTransferImpl;
import com.anytech.anytxn.authorization.service.transaction.PostAccountServiceImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthorizationLogDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.OutstandingTransactionDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DciRefundTransService单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DCI退货交易服务测试")
class DciRefundTransServiceTest {

    @Mock
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Mock
    private AuthorizationLogSelfMapper authorizationLogSelfMapper;

    @Mock
    private IOriginTransMatchProcessService originTransMatchProcessService;

    @Mock
    private RuleTransferImpl ruleTransferService;

    @Mock
    private AuthCheckDataPrepareServiceImpl authCheckDataPrepareService;

    @Mock
    private IOutstandingTransService outstandingTransService;

    @Mock
    private AuthCheckItemInspecProcessServiceImpl authCheckItemInspecProcessService;

    @Mock
    private CheckAuthResponseCodeServiceImpl checkAuthResponseCodeService;

    @Mock
    private ISystemTableService iSystemTableService;

    @Mock
    private PostAccountServiceImpl postAccountService;

    @Mock
    private AuthSmsManager authSmsManager;

    @InjectMocks
    private DciRefundTransService dciRefundTransService;

    private AuthRecordedDTO authRecordedDTO;
    private ISO8583DTO iso8583DTO;
    private AuthorizationLogDTO authorizationLogDTO;
    private OutstandingTransactionDTO outstandingTransactionDTO;
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        authRecordedDTO = new AuthRecordedDTO();
        iso8583DTO = new ISO8583DTO();
        authorizationLogDTO = new AuthorizationLogDTO();
        outstandingTransactionDTO = new OutstandingTransactionDTO();
        authorizationCheckProcessingPayload = mock(AuthorizationCheckProcessingPayload.class);
        
        // 设置基础数据
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("100.00"));
        authRecordedDTO.setOrganizationNumber("001");
        
        authorizationLogDTO.setAuthLogId("LOG123456");
        authorizationLogDTO.setGlobalFlowNumber("GLOBAL123456");
        authorizationLogDTO.setResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        authorizationLogDTO.setAuthCode("AUTH123");
        authorizationLogDTO.setCardholderBillingAmount(new BigDecimal("200.00"));
        authorizationLogDTO.setBillingCurrencyCode("USD");
        authorizationLogDTO.setTransactionTypeTopCode("P");
        authorizationLogDTO.setTransactionTypeDetailCode("00");
        authorizationLogDTO.setCustomerId("CUST123");
        authorizationLogDTO.setPostingTransactionCode("POST123");
        authorizationLogDTO.setPostingTransactionCodeRev("REV123");
        
        outstandingTransactionDTO.setAmountReturned(new BigDecimal("50.00"));
    }

    @Test
    @DisplayName("请求体检查 - 空实现")
    void testReqBodyCheck() {
        // Given & When & Then
        assertDoesNotThrow(() -> {
            dciRefundTransService.reqBodyCheck(authRecordedDTO, iso8583DTO);
        });
    }

    @Test
    @DisplayName("交易处理 - 普通消费交易借记幂等")
    void testTransHandler_DebitRepeat() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthLocalTransactionTime("**********");
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(1);
            
            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.REQUEST_IN_PROGRESS.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("0000", authRecordedDTO.getAuthCardExpirationDate());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 现金交易借记幂等")
    void testTransHandler_CashTransDebitRepeat() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode());
        authRecordedDTO.setAuthLocalTransactionTime("**********");
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(1);
            
            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.REQUEST_IN_PROGRESS.getCode(), authRecordedDTO.getAuthResponseCode());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 还款交易贷记幂等")
    void testTransHandler_RepayTransCreditRepeat() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode());
        authRecordedDTO.setAuthLocalTransactionTime("**********");
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(1);
            
            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.REQUEST_IN_PROGRESS.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("0000", authRecordedDTO.getAuthCardExpirationDate());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 匹配原交易成功且金额验证通过")
    void testTransHandler_MatchOriginalTransSuccess() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            
            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(0);
            
            Map<String, AuthorizationLogDTO> matchResult = new HashMap<>();
            matchResult.put(AuthMatchResultEnum.MATCH_SUCCESS.getCode(), authorizationLogDTO);
            when(originTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO))
                    .thenReturn(matchResult);
            
            when(outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(anyString(), anyString()))
                    .thenReturn(outstandingTransactionDTO);
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            
            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
            assertEquals(authorizationLogDTO.getGlobalFlowNumber(), authRecordedDTO.getAuthOriginalGlobalFlowNumber());
            assertEquals(authorizationLogDTO.getAuthCode(), authRecordedDTO.getAuthAuthCode());
            assertEquals(authorizationLogDTO.getBillingCurrencyCode(), authRecordedDTO.getAuthBillingCurrencyCode());
            
            verify(originTransMatchProcessService).matchRefundsOriginTrans(authRecordedDTO);
            verify(outstandingTransService).getOutstandingTransactionByGlobalFlowNumber(anyString(), anyString());
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 匹配原交易成功但退款金额超限")
    void testTransHandler_MatchOriginalTransAmountExceeded() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthCardholderBillingAmount(new BigDecimal("200.00")); // 退款金额200
        
        // 原交易金额100，已退金额50，本次退款200，总计250 > 100，应该拒绝
        authorizationLogDTO.setCardholderBillingAmount(new BigDecimal("100.00"));
        outstandingTransactionDTO.setAmountReturned(new BigDecimal("50.00"));
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(0);
            
            Map<String, AuthorizationLogDTO> matchResult = new HashMap<>();
            matchResult.put(AuthMatchResultEnum.MATCH_SUCCESS.getCode(), authorizationLogDTO);
            when(originTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO))
                    .thenReturn(matchResult);
            
            when(outstandingTransService.getOutstandingTransactionByGlobalFlowNumber(anyString(), anyString()))
                    .thenReturn(outstandingTransactionDTO);
            
            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.ACCOUNT_NO_MATCHING_CYCLE_RANGE_EXIST.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("4", authRecordedDTO.getAuthTrancactionStatus());
            assertEquals(authorizationLogDTO.getAuthCode(), authRecordedDTO.getAuthAuthCode());
            
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 匹配原交易已退货")
    void testTransHandler_MatchOriginalTransRevoked() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(0);
            
            Map<String, AuthorizationLogDTO> matchResult = new HashMap<>();
            matchResult.put(AuthMatchResultEnum.MATCH_REVACATED.getCode(), authorizationLogDTO);
            when(originTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO))
                    .thenReturn(matchResult);
            
            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.ACCOUNT_NO_MATCHING_CYCLE_RANGE_EXIST.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("4", authRecordedDTO.getAuthTrancactionStatus());
            assertEquals(authorizationLogDTO.getAuthCode(), authRecordedDTO.getAuthAuthCode());
            
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 匹配原交易错误状态")
    void testTransHandler_MatchOriginalTransError() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(0);
            
            Map<String, AuthorizationLogDTO> matchResult = new HashMap<>();
            matchResult.put("ERROR", authorizationLogDTO);
            when(originTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO))
                    .thenReturn(matchResult);
            
            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.ACCOUNT_NO_MATCHING_CYCLE_RANGE_EXIST.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("4", authRecordedDTO.getAuthTrancactionStatus());
            
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("更新数据 - 授权成功走逻辑B")
    void testUpdateData_AuthSuccess() {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        when(authorizationCheckProcessingPayload.getAuthRecordedDTO()).thenReturn(authRecordedDTO);
        
        SystemTableDTO systemTableDTO = new SystemTableDTO();
        systemTableDTO.setAuthorizationLogFlag("1");
        when(iSystemTableService.findBySystemId(anyString())).thenReturn(systemTableDTO);
        
        when(checkAuthResponseCodeService.authDataUpdateLogicB(authorizationCheckProcessingPayload))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
        when(postAccountService.postAccount(authorizationCheckProcessingPayload))
                .thenReturn("ORDER123456");
        
        // When
        int result = dciRefundTransService.updateData(authorizationCheckProcessingPayload);
        
        // Then
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        assertEquals(AuthTrancactionStatusEnum.SUCCESS_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
        assertEquals("ORDER123456", authRecordedDTO.getOrderId());
        
        verify(checkAuthResponseCodeService).authDataUpdateLogicB(authorizationCheckProcessingPayload);
        verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
    }

    @Test
    @DisplayName("更新数据 - 授权失败走逻辑C")
    void testUpdateData_AuthFailure() {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.DECLINE_GIVEN_BY_ISSUER.getCode());
        when(authorizationCheckProcessingPayload.getAuthRecordedDTO()).thenReturn(authRecordedDTO);

        SystemTableDTO systemTableDTO = new SystemTableDTO();
        systemTableDTO.setAuthorizationLogFlag("1");
        when(iSystemTableService.findBySystemId(anyString())).thenReturn(systemTableDTO);

        when(checkAuthResponseCodeService.authDataUpdateLogicC(authorizationCheckProcessingPayload))
                .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
        when(postAccountService.postAccount(authorizationCheckProcessingPayload))
                .thenReturn("ORDER123456");

        // When
        int result = dciRefundTransService.updateData(authorizationCheckProcessingPayload);

        // Then
        assertEquals(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode(), result);
        assertEquals("ORDER123456", authRecordedDTO.getOrderId());

        verify(checkAuthResponseCodeService).authDataUpdateLogicC(authorizationCheckProcessingPayload);
        verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
    }

    @Test
    @DisplayName("更新数据 - 数据更新异常")
    void testUpdateData_DataUpdateException() {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        when(authorizationCheckProcessingPayload.getAuthRecordedDTO()).thenReturn(authRecordedDTO);

        SystemTableDTO systemTableDTO = new SystemTableDTO();
        when(iSystemTableService.findBySystemId(anyString())).thenReturn(systemTableDTO);

        when(checkAuthResponseCodeService.authDataUpdateLogicB(authorizationCheckProcessingPayload))
                .thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

        // When
        int result = dciRefundTransService.updateData(authorizationCheckProcessingPayload);

        // Then
        assertEquals(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode(), result);
        assertEquals(AuthResponseCodeEnum.SYSTEM_MALFUNCTION.getCode(), authRecordedDTO.getAuthResponseCode());

        verify(checkAuthResponseCodeService).authDataUpdateLogicB(authorizationCheckProcessingPayload);
        verify(postAccountService, never()).postAccount(any());
    }

    @Test
    @DisplayName("交易处理 - 未匹配到原交易进行授权检查")
    void testTransHandler_NoMatchOriginalTransAuthCheck() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> authThreadLocalManagerStatic = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authThreadLocalManagerStatic.when(AuthThreadLocalManager::getIso8583dtoThreadLocal).thenReturn(iso8583DTO);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);

            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(0);

            Map<String, AuthorizationLogDTO> matchResult = new HashMap<>();
            when(originTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO))
                    .thenReturn(matchResult);

            Map<String, String> ruleMap = new HashMap<>();
            ruleMap.put(AuthConstans.AUTH_TRANS_TYPE_TOP_CODE, "P");
            ruleMap.put(AuthConstans.AUTH_TRANS_TYPE_DETAIL_CODE, "00");
            ruleMap.put(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE, "POST123");
            ruleMap.put(AuthConstans.AUTH_TRANS_POSTING_TRANSACTION_CODE_DEV, "REV123");
            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any()))
                    .thenReturn(ruleMap);

            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.dciProcessAuthCheck(authorizationCheckProcessingPayload))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            assertEquals("P", authRecordedDTO.getAuthTransactionTypeTopCode());
            assertEquals("00", authRecordedDTO.getAuthTransactionTypeDetailCode());
            assertEquals("POST123", authRecordedDTO.getPostingTransactionCode());
            assertEquals("REV123", authRecordedDTO.getPostingTransactionCodeRev());

            verify(ruleTransferService).getTransIdentifyCheckRule(anyString(), any());
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authCheckItemInspecProcessService).dciProcessAuthCheck(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 未匹配到原交易且授权检查异常")
    void testTransHandler_NoMatchOriginalTransAuthCheckException() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> authThreadLocalManagerStatic = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authThreadLocalManagerStatic.when(AuthThreadLocalManager::getIso8583dtoThreadLocal).thenReturn(iso8583DTO);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);

            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(0);

            Map<String, AuthorizationLogDTO> matchResult = new HashMap<>();
            when(originTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO))
                    .thenReturn(matchResult);

            when(ruleTransferService.getTransIdentifyCheckRule(anyString(), any()))
                    .thenReturn(null);

            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.dciProcessAuthCheck(authorizationCheckProcessingPayload))
                    .thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            verify(authCheckItemInspecProcessService).dciProcessAuthCheck(authorizationCheckProcessingPayload);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 未匹配到原交易且数据准备失败")
    void testTransHandler_NoMatchOriginalTransDataPrepareFailure() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthThreadLocalManager> authThreadLocalManagerStatic = mockStatic(AuthThreadLocalManager.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authThreadLocalManagerStatic.when(AuthThreadLocalManager::getIso8583dtoThreadLocal).thenReturn(iso8583DTO);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(true);

            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(0);

            Map<String, AuthorizationLogDTO> matchResult = new HashMap<>();
            when(originTransMatchProcessService.matchRefundsOriginTrans(authRecordedDTO))
                    .thenReturn(matchResult);

            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);

            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(authCheckItemInspecProcessService, never()).dciProcessAuthCheck(any());
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 通知幂等处理")
    void testTransHandler_AdviceRepeat() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode("OTHER");
        authRecordedDTO.setAuthLocalTransactionTime("**********");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(1);

            // When
            dciRefundTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            assertEquals(AuthResponseCodeEnum.REQUEST_IN_PROGRESS.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("0000", authRecordedDTO.getAuthCardExpirationDate());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }
}
