package com.anytech.anytxn.authorization.service.channel.dci.type;

import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.service.auth.AuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.enums.AuthResponseCodeEnum;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * @description DciManageTransService的单元测试类
 * <AUTHOR>
 * @date 2025/08/01
 * @version 1.0
 * @AI 已复核
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DCI管理类交易服务测试")
class DciManageTransServiceTest {

    @Mock
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @InjectMocks
    private DciManageTransService dciManageTransService;

    private AuthRecordedDTO authRecordedDTO;
    private ISO8583DTO iso8583DTO;
    private AuthorizationCheckProcessingPayload payload;

    @BeforeEach
    void setUp() {
        // 初始化OrgNumberUtils静态字段，避免NullPointerException
        OrgNumberUtils.orgNumberUtil = mock(OrgNumberUtils.class);
        lenient().when(OrgNumberUtils.getOrg()).thenReturn("001");
        lenient().when(OrgNumberUtils.orgNumberUtil.getBatchOrg()).thenReturn("001");

        // 准备测试数据
        authRecordedDTO = new AuthRecordedDTO();
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransactionAmount(new BigDecimal("100.00"));
        authRecordedDTO.setAuthCustomerId("123456");

        iso8583DTO = new ISO8583DTO();

        payload = new AuthorizationCheckProcessingPayload();
        payload.setAuthRecordedDTO(authRecordedDTO);
    }

    @Test
    @DisplayName("测试reqBodyCheck方法-空实现")
    void testReqBodyCheck_EmptyImplementation() {
        // 执行测试 - reqBodyCheck是空实现，不应该抛出异常
        assertDoesNotThrow(() -> {
            dciManageTransService.reqBodyCheck(authRecordedDTO, iso8583DTO);
        });

        // 验证没有调用其他服务
        verifyNoInteractions(authDetailDataModifyService);
    }

    @Test
    @DisplayName("测试reqBodyCheck方法-空参数")
    void testReqBodyCheck_NullParameters() {
        // 执行测试 - 空参数也不应该抛出异常
        assertDoesNotThrow(() -> {
            dciManageTransService.reqBodyCheck(null, null);
        });

        // 验证没有调用其他服务
        verifyNoInteractions(authDetailDataModifyService);
    }

    @Test
    @DisplayName("测试transHandler方法-成功执行")
    void testTransHandler_Success() throws Exception {
        // 准备数据
        when(authDetailDataModifyService.addManagementAuthLog(any(AuthRecordedDTO.class)))
                .thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> {
            dciManageTransService.transHandler(authRecordedDTO, iso8583DTO);
        });

        // 验证结果
        assertEquals(AuthResponseCodeEnum.ACCEPTED_800.getCode(), authRecordedDTO.getAuthResponseCode());
        verify(authDetailDataModifyService).addManagementAuthLog(authRecordedDTO);
    }

    @Test
    @DisplayName("测试transHandler方法-验证响应码设置")
    void testTransHandler_ResponseCodeSetting() throws Exception {
        // 准备数据
        when(authDetailDataModifyService.addManagementAuthLog(any(AuthRecordedDTO.class)))
                .thenReturn(1);

        // 执行前验证响应码为空
        assertNull(authRecordedDTO.getAuthResponseCode());

        // 执行测试
        dciManageTransService.transHandler(authRecordedDTO, iso8583DTO);

        // 验证响应码被正确设置
        assertEquals(AuthResponseCodeEnum.ACCEPTED_800.getCode(), authRecordedDTO.getAuthResponseCode());
        assertEquals("800", authRecordedDTO.getAuthResponseCode());
    }

    @Test
    @DisplayName("测试transHandler方法-addManagementAuthLog异常")
    void testTransHandler_AddManagementAuthLogException() {
        // 准备数据
        when(authDetailDataModifyService.addManagementAuthLog(any(AuthRecordedDTO.class)))
                .thenThrow(new RuntimeException("数据库操作异常"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            dciManageTransService.transHandler(authRecordedDTO, iso8583DTO);
        });

        // 验证响应码仍然被设置
        assertEquals(AuthResponseCodeEnum.ACCEPTED_800.getCode(), authRecordedDTO.getAuthResponseCode());
        verify(authDetailDataModifyService).addManagementAuthLog(authRecordedDTO);
    }

    @Test
    @DisplayName("测试transHandler方法-空参数")
    void testTransHandler_NullParameters() {
        // 执行测试 - 空参数会导致NullPointerException
        assertThrows(NullPointerException.class, () -> {
            dciManageTransService.transHandler(null, iso8583DTO);
        });

        // 验证没有调用addManagementAuthLog
        verify(authDetailDataModifyService, never()).addManagementAuthLog(any());
    }

    @Test
    @DisplayName("测试updateData方法-返回0")
    void testUpdateData_ReturnsZero() {
        // 执行测试
        int result = dciManageTransService.updateData(payload);

        // 验证结果
        assertEquals(0, result);

        // 验证没有调用其他服务
        verifyNoInteractions(authDetailDataModifyService);
    }

    @Test
    @DisplayName("测试updateData方法-空参数")
    void testUpdateData_NullPayload() {
        // 执行测试
        int result = dciManageTransService.updateData(null);

        // 验证结果
        assertEquals(0, result);

        // 验证没有调用其他服务
        verifyNoInteractions(authDetailDataModifyService);
    }

    @Test
    @DisplayName("测试updateData方法-多次调用")
    void testUpdateData_MultipleCalls() {
        // 执行多次调用
        int result1 = dciManageTransService.updateData(payload);
        int result2 = dciManageTransService.updateData(payload);
        int result3 = dciManageTransService.updateData(payload);

        // 验证结果
        assertEquals(0, result1);
        assertEquals(0, result2);
        assertEquals(0, result3);

        // 验证没有调用其他服务
        verifyNoInteractions(authDetailDataModifyService);
    }

    @Test
    @DisplayName("测试继承关系-验证是AbstractTransRoutingService的子类")
    void testInheritance() {
        // 验证继承关系
        assertTrue(dciManageTransService instanceof com.anytech.anytxn.authorization.service.channel.AbstractTransRoutingService);
    }

    @Test
    @DisplayName("测试Service注解")
    void testServiceAnnotation() {
        // 验证类上有@Service注解
        assertTrue(dciManageTransService.getClass().isAnnotationPresent(org.springframework.stereotype.Service.class));
        
        // 验证Service注解的value值
        org.springframework.stereotype.Service serviceAnnotation = 
            dciManageTransService.getClass().getAnnotation(org.springframework.stereotype.Service.class);
        assertEquals("dci_manage_trans_Service", serviceAnnotation.value());
    }

    @Test
    @DisplayName("测试transHandler方法-验证执行顺序")
    void testTransHandler_ExecutionOrder() throws Exception {
        // 准备数据
        when(authDetailDataModifyService.addManagementAuthLog(any(AuthRecordedDTO.class)))
                .thenReturn(1);

        // 执行测试
        dciManageTransService.transHandler(authRecordedDTO, iso8583DTO);

        // 验证执行顺序：先设置响应码，再调用addManagementAuthLog
        assertEquals(AuthResponseCodeEnum.ACCEPTED_800.getCode(), authRecordedDTO.getAuthResponseCode());
        verify(authDetailDataModifyService).addManagementAuthLog(authRecordedDTO);
    }

    @Test
    @DisplayName("测试transHandler方法-不同AuthRecordedDTO参数")
    void testTransHandler_DifferentAuthRecordedDTO() throws Exception {
        // 准备不同的AuthRecordedDTO
        AuthRecordedDTO anotherAuthDTO = new AuthRecordedDTO();
        anotherAuthDTO.setAuthCardNumber("9876543210987654");
        anotherAuthDTO.setAuthTransactionAmount(new BigDecimal("200.00"));
        anotherAuthDTO.setAuthCustomerId("654321");

        when(authDetailDataModifyService.addManagementAuthLog(any(AuthRecordedDTO.class)))
                .thenReturn(1);

        // 执行测试
        dciManageTransService.transHandler(anotherAuthDTO, iso8583DTO);

        // 验证结果
        assertEquals(AuthResponseCodeEnum.ACCEPTED_800.getCode(), anotherAuthDTO.getAuthResponseCode());
        verify(authDetailDataModifyService).addManagementAuthLog(anotherAuthDTO);
    }

    @Test
    @DisplayName("测试方法不抛出异常")
    void testMethodsDoNotThrowExceptions() {
        // 验证reqBodyCheck方法不抛出异常
        assertDoesNotThrow(() -> {
            dciManageTransService.reqBodyCheck(authRecordedDTO, iso8583DTO);
        });

        // 验证updateData方法不抛出异常
        assertDoesNotThrow(() -> {
            dciManageTransService.updateData(payload);
        });

        // 验证updateData方法处理null参数不抛出异常
        assertDoesNotThrow(() -> {
            dciManageTransService.updateData(null);
        });
    }

    @Test
    @DisplayName("测试transHandler方法-验证AuthDetailDataModifyService调用")
    void testTransHandler_VerifyServiceCall() throws Exception {
        // 准备数据
        when(authDetailDataModifyService.addManagementAuthLog(any(AuthRecordedDTO.class)))
                .thenReturn(1);

        // 执行测试
        dciManageTransService.transHandler(authRecordedDTO, iso8583DTO);

        // 验证方法被正确调用
        verify(authDetailDataModifyService, times(1)).addManagementAuthLog(authRecordedDTO);
        verifyNoMoreInteractions(authDetailDataModifyService);
    }
}
