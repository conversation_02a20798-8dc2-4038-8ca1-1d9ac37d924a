package com.anytech.anytxn.authorization.service.channel.dci.type;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.base.domain.model.AuthorizationLog;
import com.anytech.anytxn.authorization.base.enums.DciMTIEnum;
import com.anytech.anytxn.authorization.base.service.auth.IPreAuthorizationLogService;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.mapper.cup.AuthorizationLogSelfMapper;
import com.anytech.anytxn.authorization.service.auth.*;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.manager.AuthDataUpdateManager;
import com.anytech.anytxn.authorization.service.manager.AuthSmsManager;
import com.anytech.anytxn.authorization.service.transaction.PostAccountServiceImpl;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.authorization.domain.dto.PreAuthorizationLogDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import com.anytech.anytxn.business.dao.authorization.mapper.OutstandingTransactionSelfMapper;
import com.anytech.anytxn.business.dao.authorization.model.OutstandingTransaction;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.parameter.base.common.service.system.ISystemTableService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DciGeneralTransService单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DCI普通交易服务测试")
class DciGeneralTransServiceTest {

    @Mock
    private AuthCheckItemInspecProcessServiceImpl authCheckItemInspecProcessService;

    @Mock
    private AuthCheckDataPrepareServiceImpl authCheckDataPrepareService;

    @Mock
    private AuthCheckManager authCheckManager;

    @Mock
    private AuthSmsManager authSmsManager;

    @Mock
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Mock
    private AuthorizationLogSelfMapper authorizationLogSelfMapper;

    @Mock
    private IPreAuthorizationLogService preAuthorizationLogService;

    @Mock
    private CheckAuthResponseCodeServiceImpl checkAuthResponseCodeService;

    @Mock
    private PostAccountServiceImpl postAccountService;

    @Mock
    private AuthDataUpdateServiceImpl authDataUpdateServiceImpl;

    @Mock
    private AuthDataUpdateManager authDataUpdateManager;

    @Mock
    private ISystemTableService iSystemTableService;

    @Mock
    private OutstandingTransactionSelfMapper outstandingTransactionSelfMapper;

    @InjectMocks
    private DciGeneralTransService dciGeneralTransService;

    private AuthRecordedDTO authRecordedDTO;
    private ISO8583DTO iso8583DTO;
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;
    private CardAuthorizationDTO cardAuthorizationDTO;
    private SystemTableDTO systemInfo;
    private OrganizationInfoResDTO orgInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        authRecordedDTO = new AuthRecordedDTO();
        iso8583DTO = new ISO8583DTO();
        cardAuthorizationDTO = mock(CardAuthorizationDTO.class);
        systemInfo = new SystemTableDTO();
        orgInfo = mock(OrganizationInfoResDTO.class);
        
        // 设置基础数据
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        authRecordedDTO.setAuthCardNumber("1234567890123456");
        authRecordedDTO.setAuthTransmissionTime("0801123456");
        authRecordedDTO.setAuthSystemTraceAuditNumber("123456");
        authRecordedDTO.setPreAuthComplete(false);
        
        // 使用Mock创建AuthorizationCheckProcessingPayload
        authorizationCheckProcessingPayload = mock(AuthorizationCheckProcessingPayload.class);
        when(authorizationCheckProcessingPayload.getAuthRecordedDTO()).thenReturn(authRecordedDTO);
        when(authorizationCheckProcessingPayload.getCardAuthorizationDTO()).thenReturn(cardAuthorizationDTO);
        when(authorizationCheckProcessingPayload.getSystemInfo()).thenReturn(systemInfo);
        when(authorizationCheckProcessingPayload.getOrgInfo()).thenReturn(orgInfo);
        
        systemInfo.setAuthorizationLogFlag("1");
    }

    @Test
    @DisplayName("请求体检查 - 空实现")
    void testReqBodyCheck() {
        // Given & When & Then
        assertDoesNotThrow(() -> {
            dciGeneralTransService.reqBodyCheck(authRecordedDTO, iso8583DTO);
        });
    }

    @Test
    @DisplayName("交易处理 - 普通消费交易借记幂等")
    void testTransHandler_DebitRepeat() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        authRecordedDTO.setAuthLocalTransactionTime("0801123456");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(1);
            
            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.REQUEST_IN_PROGRESS.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("0000", authRecordedDTO.getAuthCardExpirationDate());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 现金交易借记幂等")
    void testTransHandler_CashTransDebitRepeat() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.CASH_TRANS.getCode());
        authRecordedDTO.setAuthLocalTransactionTime("0801123456");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(1);
            
            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.REQUEST_IN_PROGRESS.getCode(), authRecordedDTO.getAuthResponseCode());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - 还款交易贷记幂等")
    void testTransHandler_RepayTransCreditRepeat() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.REPAY_TRANS.getCode());
        authRecordedDTO.setAuthLocalTransactionTime("0801123456");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(authorizationLogSelfMapper.isExistRepeat(anyString(), anyString(), anyString()))
                    .thenReturn(1);

            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            assertEquals(AuthResponseCodeEnum.REQUEST_IN_PROGRESS.getCode(), authRecordedDTO.getAuthResponseCode());
            assertEquals("0000", authRecordedDTO.getAuthCardExpirationDate());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
        }
    }

    @Test
    @DisplayName("交易处理 - DCI授权通知交易")
    void testTransHandler_DciAuthAdvice() throws Exception {
        // Given
        authRecordedDTO.setAuthMessageTypeId(DciMTIEnum.DCI_AUTH_ADVICE.getCode());
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.selectByCardNumberAndTransMission(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            when(postAccountService.postAccount(authorizationCheckProcessingPayload))
                    .thenReturn("ORDER123456");
            
            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals("ORDER123456", authRecordedDTO.getOrderId());
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authCheckItemInspecProcessService).processAuthCheck(authorizationCheckProcessingPayload, null);
            verify(checkAuthResponseCodeService).authDataUpdateLogicB(authorizationCheckProcessingPayload);
            verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
            verify(authSmsManager).sendMsgToEai(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 预授权完成交易")
    void testTransHandler_PreAuthComplete() throws Exception {
        // Given
        authRecordedDTO.setPreAuthComplete(true);
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            
            when(authorizationLogSelfMapper.selectByCardNumberAndTransMission(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            when(postAccountService.postAccount(authorizationCheckProcessingPayload))
                    .thenReturn("ORDER123456");
            
            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals("ORDER123456", authRecordedDTO.getOrderId());
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 普通授权检查成功")
    void testTransHandler_AuthCheckSuccess() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());
        
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(true);
            
            when(authorizationLogSelfMapper.selectByCardNumberAndTransMission(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            when(authCheckManager.arqcCheckDiners(authorizationCheckProcessingPayload, true))
                    .thenReturn(authRecordedDTO);
            when(postAccountService.postAccount(authorizationCheckProcessingPayload))
                    .thenReturn("ORDER123456");
            
            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals("ORDER123456", authRecordedDTO.getOrderId());
            verify(authCheckManager).arqcCheckDiners(authorizationCheckProcessingPayload, true);
            verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - ARQC校验失败")
    void testTransHandler_ArqcCheckFailure() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(true);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(any(AuthRecordedDTO.class))).thenReturn(true);

            when(authorizationLogSelfMapper.selectByCardNumberAndTransMission(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());

            AuthRecordedDTO arqcFailedDTO = new AuthRecordedDTO();
            arqcFailedDTO.setAuthResponseCode(AuthResponseCodeEnum.DECLINE_GIVEN_BY_ISSUER.getCode());
            when(authCheckManager.arqcCheckDiners(authorizationCheckProcessingPayload, true))
                    .thenReturn(arqcFailedDTO);

            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            assertEquals(AuthConstans.AUTH_CHECK_RESULT_REJECT, authRecordedDTO.getArqcCheckResultCode());
            assertEquals(AuthResponseCodeEnum.DECLINE_GIVEN_BY_ISSUER.getCode(), authRecordedDTO.getAuthResponseCode());
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 授权检查失败")
    void testTransHandler_AuthCheckFailure() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(true);

            when(authorizationLogSelfMapper.selectByCardNumberAndTransMission(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);

            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 授权检查异常")
    void testTransHandler_AuthCheckException() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);

            when(authorizationLogSelfMapper.selectByCardNumberAndTransMission(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());

            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authCheckItemInspecProcessService).processAuthCheck(authorizationCheckProcessingPayload, null);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 通知幂等返回false")
    void testTransHandler_AdviceRepeatFalse() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode("OTHER");

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);

            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            when(postAccountService.postAccount(authorizationCheckProcessingPayload))
                    .thenReturn("ORDER123456");

            // When
            dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);

            // Then
            assertEquals("ORDER123456", authRecordedDTO.getOrderId());
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(postAccountService).postAccount(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 数据准备异常")
    void testTransHandler_DataPrepareException() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode(AuthTransTypeTopCodeEnum.NORMAL_COMNSUMER_TRANS.getCode());

        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {

            orgUtils.when(OrgNumberUtils::getOrg).thenReturn("001");

            when(authorizationLogSelfMapper.selectByCardNumberAndTransMission(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenThrow(new RuntimeException("Data prepare failed"));

            // When & Then
            assertThrows(RuntimeException.class, () -> {
                dciGeneralTransService.transHandler(authRecordedDTO, iso8583DTO);
            });

            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("更新数据 - 返回0")
    void testUpdateData() {
        // Given & When
        int result = dciGeneralTransService.updateData(authorizationCheckProcessingPayload);

        // Then
        assertEquals(0, result);
    }
}
