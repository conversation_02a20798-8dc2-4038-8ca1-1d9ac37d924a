package com.anytech.anytxn.authorization.service.channel.dci.type;

import com.anytech.anytxn.authorization.base.constants.AuthConstans;
import com.anytech.anytxn.authorization.base.domain.dto.ISO8583DTO;
import com.anytech.anytxn.authorization.bo.AuthorizationCheckProcessingPayload;
import com.anytech.anytxn.authorization.service.auth.AuthCheckDataPrepareServiceImpl;
import com.anytech.anytxn.authorization.service.auth.AuthCheckItemInspecProcessServiceImpl;
import com.anytech.anytxn.authorization.service.auth.AuthDetailDataModifyServiceImpl;
import com.anytech.anytxn.authorization.service.auth.CheckAuthResponseCodeServiceImpl;
import com.anytech.anytxn.authorization.service.manager.AuthCheckManager;
import com.anytech.anytxn.authorization.service.manager.AuthDataUpdateManager;
import com.anytech.anytxn.business.base.authorization.domain.dto.AuthRecordedDTO;
import com.anytech.anytxn.business.base.card.domain.dto.CardAuthorizationDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.SystemTableDTO;
import com.anytech.anytxn.business.base.authorization.enums.*;
import com.anytech.anytxn.business.base.monetary.domain.bo.CustAccountBO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DciBalanceInquiryTransService单元测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DCI余额查询交易服务测试")
class DciBalanceInquiryTransServiceTest {

    @Mock
    private AuthCheckItemInspecProcessServiceImpl authCheckItemInspecProcessService;

    @Mock
    private AuthCheckDataPrepareServiceImpl authCheckDataPrepareService;

    @Mock
    private AuthCheckManager authCheckManager;

    @Mock
    private AuthDetailDataModifyServiceImpl authDetailDataModifyService;

    @Mock
    private CheckAuthResponseCodeServiceImpl checkAuthResponseCodeService;

    @Mock
    private AuthDataUpdateManager authDataUpdateManager;

    @InjectMocks
    private DciBalanceInquiryTransService dciBalanceInquiryTransService;

    private AuthRecordedDTO authRecordedDTO;
    private ISO8583DTO iso8583DTO;
    private AuthorizationCheckProcessingPayload authorizationCheckProcessingPayload;
    private CardAuthorizationDTO cardAuthorizationDTO;
    private SystemTableDTO systemInfo;
    private OrganizationInfoResDTO orgInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        authRecordedDTO = new AuthRecordedDTO();
        iso8583DTO = new ISO8583DTO();
        cardAuthorizationDTO = mock(CardAuthorizationDTO.class);
        systemInfo = new SystemTableDTO();
        orgInfo = mock(OrganizationInfoResDTO.class);

        // 设置基础数据
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.APPROVED_BY_ISSUER.getCode());
        authRecordedDTO.setAuthTransactionTypeCode(AuthTransTypeEnum.NORMAL_TRANS.getCode());
        authRecordedDTO.setAuthProcessingCode("000000");
        authRecordedDTO.setAuthTransactionTypeTopCode("P");

        // 使用Mock创建AuthorizationCheckProcessingPayload
        authorizationCheckProcessingPayload = mock(AuthorizationCheckProcessingPayload.class);
        when(authorizationCheckProcessingPayload.getAuthRecordedDTO()).thenReturn(authRecordedDTO);
        when(authorizationCheckProcessingPayload.getCardAuthorizationDTO()).thenReturn(cardAuthorizationDTO);
        when(authorizationCheckProcessingPayload.getSystemInfo()).thenReturn(systemInfo);
        when(authorizationCheckProcessingPayload.getOrgInfo()).thenReturn(orgInfo);

        systemInfo.setAuthorizationLogFlag("1");
    }

    @Test
    @DisplayName("请求体检查 - 空实现")
    void testReqBodyCheck() {
        // Given & When & Then
        assertDoesNotThrow(() -> {
            dciBalanceInquiryTransService.reqBodyCheck(authRecordedDTO, iso8583DTO);
        });
    }

    @Test
    @DisplayName("交易处理 - 数据准备失败场景")
    void testTransHandler_DataPrepareFailure() throws Exception {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.SYSTEM_MALFUNCTION.getCode());
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(true);
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 普通交易授权检查成功")
    void testTransHandler_NormalTransSuccess() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(true);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authCheckItemInspecProcessService).processAuthCheck(authorizationCheckProcessingPayload, null);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 授权检查异常")
    void testTransHandler_AuthCheckException() throws Exception {
        // Given
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(true);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.EXCEPTION_CODE.getCode());
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            verify(authCheckDataPrepareService).prepareAuthData(authRecordedDTO);
            verify(authCheckItemInspecProcessService).processAuthCheck(authorizationCheckProcessingPayload, null);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 账户验证交易")
    void testTransHandler_AccountVerification() throws Exception {
        // Given
        authRecordedDTO.setAuthProcessingCode("180000");
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(true);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthResponseCodeEnum.CARD_VERIFY_SUCCESS.getCode(), authRecordedDTO.getAuthResponseCode());
            verify(authDetailDataModifyService).modifyAuthorizationLog(authRecordedDTO);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - POS/ATM查询交易")
    void testTransHandler_PosAtmQuery() throws Exception {
        // Given
        authRecordedDTO.setAuthTransactionTypeTopCode("Q");
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(true);
            when(authCheckItemInspecProcessService.processAuthCheck(authorizationCheckProcessingPayload, null))
                    .thenReturn(AuthItemCheckResCodeEnum.APPROVE_CODE.getCode());
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            verify(checkAuthResponseCodeService).authDataUpdateLogicD(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 卡验证成功")
    void testTransHandler_CardVerifySuccess() throws Exception {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.CARD_VERIFY_SUCCESS.getCode());
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(false);
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            verify(authDetailDataModifyService).modifyAuthorizationLog(authRecordedDTO);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 授权拒绝场景")
    void testTransHandler_AuthorizationRejected() throws Exception {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.SYSTEM_MALFUNCTION.getCode());
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<AuthDetailDataModifyServiceImpl> authDetailStatic = mockStatic(AuthDetailDataModifyServiceImpl.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(false);
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthTrancactionStatusEnum.ERROR_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
            authDetailStatic.verify(() -> AuthDetailDataModifyServiceImpl.updateDlyAcctErrCnt(
                    cardAuthorizationDTO, authRecordedDTO, orgInfo, false));
            verify(authDetailDataModifyService).addAuthorizationLog(authRecordedDTO);
            verify(authDataUpdateManager).updateAuthorization(authorizationCheckProcessingPayload);
            verify(checkAuthResponseCodeService).authDataUpdateLogicC(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 授权日志标志为否")
    void testTransHandler_AuthLogFlagNo() throws Exception {
        // Given
        authRecordedDTO.setAuthResponseCode(AuthResponseCodeEnum.SYSTEM_MALFUNCTION.getCode());
        systemInfo.setAuthorizationLogFlag("0");
        
        try (MockedStatic<AuthCheckManager> authCheckManagerStatic = mockStatic(AuthCheckManager.class);
             MockedStatic<AuthDetailDataModifyServiceImpl> authDetailStatic = mockStatic(AuthDetailDataModifyServiceImpl.class);
             MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenReturn(authorizationCheckProcessingPayload);
            authCheckManagerStatic.when(() -> AuthCheckManager.isFailure(authRecordedDTO)).thenReturn(false);
            authCheckManagerStatic.when(() -> AuthCheckManager.isSuccess(authRecordedDTO)).thenReturn(false);
            
            // When
            dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            
            // Then
            assertEquals(AuthTrancactionStatusEnum.ERROR_STATUS.getCode(), authRecordedDTO.getAuthTrancactionStatus());
            authDetailStatic.verify(() -> AuthDetailDataModifyServiceImpl.updateDlyAcctErrCnt(
                    cardAuthorizationDTO, authRecordedDTO, orgInfo, false));
            verify(authDetailDataModifyService, never()).addAuthorizationLog(authRecordedDTO);
            verify(authDataUpdateManager).updateAuthorization(authorizationCheckProcessingPayload);
            verify(checkAuthResponseCodeService).authDataUpdateLogicC(authorizationCheckProcessingPayload);
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("交易处理 - 异常抛出时清理ThreadLocal")
    void testTransHandler_ExceptionThrown() throws Exception {
        // Given
        try (MockedStatic<CustAccountBO> custAccountBOStatic = mockStatic(CustAccountBO.class)) {
            
            when(authCheckDataPrepareService.prepareAuthData(authRecordedDTO))
                    .thenThrow(new RuntimeException("Test exception"));
            
            // When & Then
            assertThrows(RuntimeException.class, () -> {
                dciBalanceInquiryTransService.transHandler(authRecordedDTO, iso8583DTO);
            });
            
            custAccountBOStatic.verify(CustAccountBO::clearThreadLocalNotBatch);
        }
    }

    @Test
    @DisplayName("更新数据 - 返回0")
    void testUpdateData() {
        // Given & When
        int result = dciBalanceInquiryTransService.updateData(authorizationCheckProcessingPayload);
        
        // Then
        assertEquals(0, result);
    }
}
