package com.anytech.anytxn.authorization.service.fegin.cardholder;

import com.anytech.anytxn.authorization.client.cardholder.AuthCardHolderFeign;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerAuthorizationInfoDTO;
import com.anytech.anytxn.business.base.customer.domain.dto.CustomerBasicInfoDTO;
import com.anytech.anytxn.common.core.base.AnyTxnHttpResponse;
import com.anytech.anytxn.business.base.authorization.enums.AnyTxnAuthRespCodeEnum;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * CardHolderFeign回退处理类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-23
 */
@Service
@Slf4j
public class CardHolderFeignFallBack implements FallbackFactory<AuthCardHolderFeign> {

    @Override
    public AuthCardHolderFeign create(Throwable throwable) {
        log.warn("调用CardHolder服务异常: {}", throwable != null ? throwable.getMessage() : "未知异常");
        
        return new AuthCardHolderFeign() {
            @Override
            public AnyTxnHttpResponse<CustomerAuthorizationInfoDTO> getCustomerAuthInfoOrgAndCid(
                    String organizationNumber, String customerId, String cid) {
                return AnyTxnHttpResponse.fail(
                    AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), 
                    "CardHolder服务不可用，无法获取客户授权信息"
                );
            }

            @Override
            public AnyTxnHttpResponse<CustomerBasicInfoDTO> getCustomerBasicInfoByOrgAndCid(
                    String organizationNumber, String customerId, String cid) {
                return AnyTxnHttpResponse.fail(
                    AnyTxnAuthRespCodeEnum.S_SERVER_NO_INSTANCE.getCode(), 
                    "CardHolder服务不可用，无法获取客户基本信息"
                );
            }
        };
    }
}
